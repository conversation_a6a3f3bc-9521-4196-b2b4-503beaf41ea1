-- 告警订阅相关表结构

-- 1. 订阅规则表
CREATE TABLE alarm_subscription (
    id VARCHAR(32) PRIMARY KEY COMMENT '订阅ID',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    subscription_name VARCHAR(100) NOT NULL COMMENT '订阅名称',
    description TEXT COMMENT '订阅描述',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    priority_level INT DEFAULT 1 COMMENT '优先级(1-5)',
    
    -- 匹配规则 (JSON格式存储)
    match_rules JSON COMMENT '匹配规则列表',
    
    -- 通知设置
    notification_channels JSON COMMENT '通知渠道配置',
    frequency_type VARCHAR(20) DEFAULT 'REAL_TIME' COMMENT '通知频率类型',
    frequency_config JSON COMMENT '频率控制配置',
    
    -- 免打扰设置
    quiet_hours_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用免打扰',
    quiet_hours_config JSON COMMENT '免打扰时间配置',
    
    -- 统计信息
    trigger_count INT DEFAULT 0 COMMENT '触发次数',
    last_triggered_time DATETIME COMMENT '最后触发时间',
    
    -- 审计字段
    created_by VARCHAR(32) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_enabled (enabled),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB COMMENT='告警订阅规则表';

-- 2. 通知模板表
CREATE TABLE notification_template (
    id VARCHAR(32) PRIMARY KEY COMMENT '模板ID',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_type VARCHAR(20) NOT NULL COMMENT '模板类型(EMAIL/KAFKA)',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认模板',
    
    -- 模板内容
    subject_template TEXT COMMENT '主题模板',
    content_template TEXT NOT NULL COMMENT '内容模板',
    template_variables JSON COMMENT '模板变量说明',
    
    -- 审计字段
    created_by VARCHAR(32) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_template_type (template_type),
    INDEX idx_is_default (is_default)
) ENGINE=InnoDB COMMENT='通知模板表';

-- 3. 通知发送记录表
CREATE TABLE notification_log (
    id VARCHAR(32) PRIMARY KEY COMMENT '记录ID',
    subscription_id VARCHAR(32) NOT NULL COMMENT '订阅ID',
    alarm_id VARCHAR(32) NOT NULL COMMENT '告警ID',
    channel_type VARCHAR(20) NOT NULL COMMENT '通知渠道类型',
    recipient VARCHAR(200) NOT NULL COMMENT '接收者',
    
    -- 发送状态
    send_status VARCHAR(20) NOT NULL COMMENT '发送状态(SUCCESS/FAILED/PENDING)',
    send_time DATETIME NOT NULL COMMENT '发送时间',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    
    -- 内容信息
    subject VARCHAR(500) COMMENT '通知主题',
    content TEXT COMMENT '通知内容',
    
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_alarm_id (alarm_id),
    INDEX idx_send_time (send_time),
    INDEX idx_send_status (send_status)
) ENGINE=InnoDB COMMENT='通知发送记录表';

-- 插入默认通知模板
INSERT INTO notification_template (id, template_name, template_type, is_default, subject_template, content_template, template_variables) VALUES
('default_email_template', '默认邮件模板', 'EMAIL', TRUE, 
 '【NTA安全告警】${alarmType} - ${alarmLevel}',
 '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>安全告警通知</title>
</head>
<body>
    <h2>安全告警通知</h2>
    <table border="1" cellpadding="5" cellspacing="0">
        <tr><td><strong>告警类型</strong></td><td>${alarmType}</td></tr>
        <tr><td><strong>告警级别</strong></td><td>${alarmLevel}</td></tr>
        <tr><td><strong>告警时间</strong></td><td>${alarmTime}</td></tr>
        <tr><td><strong>源IP</strong></td><td>${srcIp}</td></tr>
        <tr><td><strong>目标IP</strong></td><td>${dstIp}</td></tr>
        <tr><td><strong>告警描述</strong></td><td>${description}</td></tr>
    </table>
    <p>请及时处理该安全告警。</p>
</body>
</html>',
 '{"alarmType": "告警类型", "alarmLevel": "告警级别", "alarmTime": "告警时间", "srcIp": "源IP", "dstIp": "目标IP", "description": "告警描述"}'),

('default_kafka_template', '默认Kafka模板', 'KAFKA', TRUE,
 '${alarmType}告警',
 '{
    "alarmId": "${alarmId}",
    "alarmType": "${alarmType}",
    "alarmLevel": "${alarmLevel}",
    "alarmTime": "${alarmTime}",
    "srcIp": "${srcIp}",
    "dstIp": "${dstIp}",
    "description": "${description}",
    "notificationTime": "${notificationTime}"
}',
 '{"alarmId": "告警ID", "alarmType": "告警类型", "alarmLevel": "告警级别", "alarmTime": "告警时间", "srcIp": "源IP", "dstIp": "目标IP", "description": "告警描述", "notificationTime": "通知时间"}');
