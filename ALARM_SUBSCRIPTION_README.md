# 告警订阅/通知功能重构说明

## 概述

本次重构实现了告警订阅/通知功能的职责分离架构，将订阅管理和通知发送分别放在不同的模块中，提高了系统的可维护性和扩展性。

## 架构设计

### 职责分离

- **`services/alarm` 模块**：负责告警订阅的管理（CRUD、配置、用户界面）
- **`flink-jobs/alarm-processor` 模块**：负责告警通知的发送（实时匹配、高性能发送）

### 数据流向

```
用户 → services/alarm → 订阅配置存储
                ↓
告警事件 → alarm-processor ← 拉取订阅配置
                ↓
            匹配 + 发送通知
                ↓
            记录通知结果 → services/alarm
```

## 功能特性

### 支持的通知方式

1. **邮件通知**：支持 SMTP 协议发送邮件
2. **Kafka 通知**：发送消息到指定的 Kafka Topic

### 订阅规则匹配

支持多种匹配操作符：
- `EQUALS`：等于
- `NOT_EQUALS`：不等于
- `CONTAINS`：包含
- `NOT_CONTAINS`：不包含
- `REGEX`：正则匹配
- `GREATER_THAN`：大于
- `LESS_THAN`：小于
- `IN`：在列表中
- `NOT_IN`：不在列表中

### 通知频率控制

- **实时通知**：告警产生时立即发送
- **间隔通知**：按时间间隔发送
- **批量通知**：达到阈值后批量发送
- **摘要通知**：定期发送摘要

### 免打扰功能

- 支持设置免打扰时间段
- 支持按星期几设置
- 紧急告警可忽略免打扰设置

## 模块详细说明

### services/alarm 模块

#### 新增的主要组件

1. **数据模型**
   - `AlarmSubscription`：订阅实体
   - `NotificationTemplate`：通知模板
   - `NotificationLog`：通知记录

2. **DTO 类**
   - `CreateSubscriptionRequest`：创建订阅请求
   - `UpdateSubscriptionRequest`：更新订阅请求
   - `AlarmSubscriptionVo`：订阅响应
   - `TestSubscriptionRequest`：测试订阅请求
   - `NotificationSubscriptionDto`：供 alarm-processor 使用的订阅 DTO

3. **Service 层**
   - `AlarmSubscriptionService`：订阅管理服务
   - `AlarmSubscriptionServiceImpl`：服务实现

4. **Controller 层**
   - `AlarmSubscriptionController`：订阅管理 API
   - `SubscriptionSyncController`：配置同步 API

5. **Mapper 层**
   - `AlarmSubscriptionMapper`：订阅数据访问
   - `NotificationTemplateMapper`：模板数据访问
   - `NotificationLogMapper`：日志数据访问

#### 主要 API 接口

```http
# 订阅管理
POST   /alarm/subscription              # 创建订阅
PUT    /alarm/subscription/{id}         # 更新订阅
DELETE /alarm/subscription/{id}         # 删除订阅
GET    /alarm/subscription              # 获取订阅列表
GET    /alarm/subscription/{id}         # 获取订阅详情
PUT    /alarm/subscription/{id}/status  # 启用/禁用订阅
POST   /alarm/subscription/test         # 测试订阅规则

# 配置同步（供 alarm-processor 调用）
GET    /alarm/subscription/sync/active           # 获取启用的订阅
GET    /alarm/subscription/sync/updated          # 获取更新的订阅
POST   /alarm/subscription/sync/notification-result  # 记录通知结果
GET    /alarm/subscription/sync/health           # 健康检查
```

### flink-jobs/alarm-processor 模块

#### 新增的主要组件

1. **通知发送器**
   - `SimplifiedNotificationSender`：简化的通知发送器，支持邮件和 Kafka

2. **配置同步**
   - `AlarmServiceClient`：与 services/alarm 通信的客户端
   - `SubscriptionConfigSyncService`：订阅配置同步服务

3. **流处理函数**
   - `SimplifiedNotificationFunction`：通知处理函数，使用广播状态管理订阅配置

4. **流水线**
   - `SimplifiedNotificationPipeline`：简化的通知处理流水线

#### 配置项

```properties
# 邮件配置
alarm.processor.notification.email.smtp.host=smtp.example.com
alarm.processor.notification.email.smtp.port=587
alarm.processor.notification.email.username=<EMAIL>
alarm.processor.notification.email.password=your_password
alarm.processor.notification.email.from.address=<EMAIL>

# Kafka 通知配置
alarm.processor.notification.kafka.bootstrap.servers=localhost:9092
alarm.processor.notification.kafka.acks=1
alarm.processor.notification.kafka.retries=3

# 告警服务配置
alarm.processor.notification.alarmServiceBaseUrl=http://localhost:8080
alarm.processor.notification.subscriptionRefreshIntervalSeconds=30
```

## 数据库表结构

### 1. alarm_subscription（订阅规则表）

```sql
CREATE TABLE alarm_subscription (
    id VARCHAR(32) PRIMARY KEY,
    user_id VARCHAR(32) NOT NULL,
    subscription_name VARCHAR(100) NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    priority_level INT DEFAULT 1,
    match_rules JSON,
    notification_channels JSON,
    frequency_type VARCHAR(20) DEFAULT 'REAL_TIME',
    frequency_config JSON,
    quiet_hours_enabled BOOLEAN DEFAULT FALSE,
    quiet_hours_config JSON,
    trigger_count INT DEFAULT 0,
    last_triggered_time DATETIME,
    created_by VARCHAR(32),
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(32),
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. notification_template（通知模板表）

```sql
CREATE TABLE notification_template (
    id VARCHAR(32) PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL,
    template_type VARCHAR(20) NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    subject_template TEXT,
    content_template TEXT NOT NULL,
    template_variables JSON,
    created_by VARCHAR(32),
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(32),
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. notification_log（通知发送记录表）

```sql
CREATE TABLE notification_log (
    id VARCHAR(32) PRIMARY KEY,
    subscription_id VARCHAR(32) NOT NULL,
    alarm_id VARCHAR(32) NOT NULL,
    channel_type VARCHAR(20) NOT NULL,
    recipient VARCHAR(200) NOT NULL,
    send_status VARCHAR(20) NOT NULL,
    send_time DATETIME NOT NULL,
    error_message TEXT,
    retry_count INT DEFAULT 0,
    subject VARCHAR(500),
    content TEXT
);
```

## 使用示例

### 1. 创建邮件订阅

```json
{
  "subscriptionName": "高危恶意软件告警",
  "description": "监控高危级别的恶意软件告警",
  "priorityLevel": 1,
  "matchRules": [
    {
      "fieldName": "alarmType",
      "operator": "EQUALS",
      "expectedValue": "恶意软件"
    },
    {
      "fieldName": "alarmLevel",
      "operator": "IN",
      "expectedValue": "HIGH,CRITICAL"
    }
  ],
  "notificationChannels": [
    {
      "channelType": "EMAIL",
      "address": "<EMAIL>",
      "templateId": "default_email_template",
      "enabled": true
    }
  ],
  "frequencyType": "REAL_TIME"
}
```

### 2. 创建 Kafka 订阅

```json
{
  "subscriptionName": "内网横向移动告警",
  "description": "监控内网横向移动攻击",
  "matchRules": [
    {
      "fieldName": "attackFamily",
      "operator": "CONTAINS",
      "expectedValue": "横向移动"
    },
    {
      "fieldName": "srcIp",
      "operator": "CONTAINS",
      "expectedValue": "192.168"
    }
  ],
  "notificationChannels": [
    {
      "channelType": "KAFKA",
      "address": "lateral-movement-alerts",
      "templateId": "default_kafka_template",
      "enabled": true
    }
  ],
  "frequencyType": "INTERVAL",
  "frequencyConfig": {
    "intervalMinutes": 5
  }
}
```

### 3. 测试订阅规则

```json
{
  "matchRules": [
    {
      "fieldName": "alarmType",
      "operator": "EQUALS",
      "expectedValue": "恶意软件"
    }
  ],
  "testData": {
    "alarmType": "恶意软件",
    "alarmLevel": "HIGH",
    "srcIp": "*************",
    "dstIp": "********"
  }
}
```

## 部署和配置

### 1. 数据库初始化

```bash
# 执行数据库脚本
mysql -u root -p nta < services/alarm/subscription_tables.sql
```

### 2. 配置 services/alarm

在 `application.yml` 中添加数据库配置：

```yaml
spring:
  datasource:
    url: *******************************
    username: nta_user
    password: nta_password
```

### 3. 配置 alarm-processor

复制并修改配置文件：

```bash
cp flink-jobs/alarm-processor/src/main/resources/alarm-processor-example.properties \
   flink-jobs/alarm-processor/src/main/resources/alarm-processor.properties
```

### 4. 启动服务

```bash
# 启动 services/alarm
cd services/alarm
mvn spring-boot:run

# 启动 alarm-processor
cd flink-jobs/alarm-processor
mvn exec:java -Dexec.mainClass="com.geeksec.alarmprocessor.job.AlarmProcessorJob"
```

## 测试

### 运行单元测试

```bash
cd services/alarm
mvn test
```

### 运行集成测试

```bash
cd services/alarm
mvn test -Dtest=AlarmSubscriptionIntegrationTest
```

## 监控和运维

### 1. 监控指标

- 订阅配置同步状态
- 通知发送成功率
- 通知发送延迟
- 规则匹配性能

### 2. 日志查看

```bash
# 查看 services/alarm 日志
tail -f services/alarm/logs/application.log

# 查看 alarm-processor 日志
tail -f flink-jobs/alarm-processor/logs/alarm-processor.log
```

### 3. 健康检查

```bash
# 检查 services/alarm 健康状态
curl http://localhost:8080/alarm/subscription/sync/health

# 检查 alarm-processor 状态
curl http://localhost:8081/jobs
```

## 扩展和定制

### 1. 添加新的通知渠道

1. 在 `NotificationChannelDto.ChannelType` 中添加新类型
2. 在 `SimplifiedNotificationSender` 中实现发送逻辑
3. 更新相关的配置和测试

### 2. 添加新的匹配操作符

1. 在 `SubscriptionRuleDto.OperatorType` 中添加新操作符
2. 在 `SimplifiedNotificationFunction.matchesRule` 中实现匹配逻辑
3. 更新测试用例

### 3. 自定义通知模板

1. 在 `notification_template` 表中添加新模板
2. 支持更多的模板变量
3. 实现模板渲染逻辑

## 注意事项

1. **性能考虑**：订阅规则匹配在 Flink 流处理中进行，需要注意规则复杂度对性能的影响
2. **数据一致性**：订阅配置的同步有延迟，需要考虑配置变更的生效时间
3. **容错处理**：通知发送失败时的重试机制和错误处理
4. **安全性**：邮件密码等敏感信息需要加密存储
5. **扩展性**：随着订阅数量增长，需要考虑缓存和性能优化

## 后续优化建议

1. 实现订阅配置的实时推送（通过 Kafka 事件）
2. 添加通知发送的限流和熔断机制
3. 实现更丰富的通知模板和变量替换
4. 添加通知发送的统计和分析功能
5. 支持更多的通知渠道（短信、钉钉、企业微信等）
