# 告警通知架构重新设计总结

## 概述

根据您的正确指导，我们重新设计了告警通知架构，明确了模块职责分工：

- **services/alarm（告警管理服务）**：负责告警订阅的 CRUD 管理
- **flink-jobs/alarm-processor（告警处理器）**：专注于告警推送功能

## 新架构设计

### 模块职责分工

#### 1. services/alarm（告警管理服务）
- **告警订阅管理**：提供订阅的增删改查功能
- **订阅规则配置**：管理复杂的订阅规则和条件
- **用户权限管理**：控制用户对订阅的访问权限
- **REST API 服务**：提供前端和其他服务调用的接口
- **变更通知**：订阅配置变更时发送消息通知

#### 2. flink-jobs/alarm-processor（告警处理器）
- **告警推送执行**：根据订阅配置执行实际的通知发送
- **订阅配置获取**：从 services/alarm 获取订阅信息
- **Broadcast State 管理**：使用 Flink 的 Broadcast State 管理订阅配置
- **多渠道通知**：支持邮件、短信、钉钉、企业微信、飞书等多种通知渠道

### 数据流设计

```
1. 订阅管理流程：
   前端 → services/alarm → 数据库

2. 配置同步流程：
   services/alarm → Kafka(订阅变更消息) → alarm-processor(Broadcast State)

3. 告警推送流程：
   告警事件 → alarm-processor → 订阅匹配 → 通知发送
```

## 已实现的组件

### 1. 通知模型层 (`notification/model/`)

#### NotificationSubscription.java
- 专门用于推送的订阅信息模型
- 包含订阅规则、通知渠道、频率控制等
- 支持免打扰时间、优先级管理

#### NotificationRule.java
- 灵活的规则匹配引擎
- 支持多种操作符：等于、包含、正则匹配、大于、小于、IN、NOT_IN 等
- 支持字段提取和反射调用

#### NotificationChannel.java
- 多种通知渠道支持
- 渠道可用性验证
- 发送统计和成功率跟踪

#### NotificationTemplate.java
- 多渠道模板支持
- 变量替换和模板渲染
- 支持扩展模板引擎（Freemarker、Velocity、Thymeleaf）

### 2. 通知发送层 (`notification/sender/`)

#### NotificationSender.java
- 统一的通知发送接口
- 支持多种渠道的发送器
- 可扩展的渠道发送器架构

### 3. 通知管理层 (`notification/manager/`)

#### NotificationManager.java
- 告警通知处理的核心组件
- 订阅匹配和通知发送协调
- 统计信息收集和性能监控

### 4. 客户端层 (`notification/client/`)

#### AlarmServiceClient.java
- 与 services/alarm 的 HTTP 客户端
- 异步获取订阅信息
- 健康检查和重试机制

### 5. Broadcast State 层 (`notification/broadcast/`)

#### SubscriptionBroadcastFunction.java
- Flink Broadcast State 处理函数
- 动态更新订阅配置
- 告警流和配置流的连接处理

#### SubscriptionUpdateEvent.java
- 订阅更新事件模型
- 支持创建、更新、删除、批量更新等事件类型
- 完整的事件追踪信息

### 6. 数据源层 (`notification/source/`)

#### SubscriptionUpdateSource.java
- 订阅配置更新数据源
- 定期从 services/alarm 获取配置
- 支持手动和自动刷新模式

## 技术特性

### 1. Broadcast State 模式
- **动态配置更新**：支持订阅配置的实时更新
- **状态一致性**：确保所有算子实例的配置一致
- **容错恢复**：支持从检查点恢复配置状态

### 2. 多渠道通知
- **邮件通知**：支持 HTML 模板和 SMTP 配置
- **短信通知**：支持多种短信服务商
- **即时通讯**：钉钉、企业微信、飞书
- **Webhook**：支持自定义 HTTP 回调
- **扩展性**：可轻松添加新的通知渠道

### 3. 灵活规则引擎
- **多种操作符**：等于、包含、正则匹配、数值比较等
- **字段提取**：支持反射和 getter 方法
- **复合条件**：支持 AND/OR 逻辑组合
- **缓存优化**：规则编译结果缓存

### 4. 频率控制
- **实时通知**：立即发送
- **间隔通知**：按时间间隔发送
- **批量通知**：达到阈值后批量发送
- **摘要通知**：定期发送摘要
- **免打扰时间**：支持时间段和日期设置

### 5. 性能优化
- **异步处理**：通知发送异步执行
- **连接池**：HTTP 客户端连接池
- **缓存机制**：订阅、模板、规则缓存
- **批量操作**：支持批量获取和更新

## 配置示例

### alarm-processor 配置
```yaml
alarm-processor:
  notification:
    enabled: true
    passthrough-alarms: true
    parallelism: 2
    alarm-service-base-url: "http://localhost:8080"
    subscription-refresh-interval-seconds: 300
    subscription-periodic-refresh-enabled: true
```

### services/alarm API 接口设计
```
GET  /api/v1/subscriptions/active          # 获取所有有效订阅
GET  /api/v1/subscriptions/user/{userId}   # 获取用户订阅
GET  /api/v1/subscriptions/{id}            # 获取单个订阅
POST /api/v1/subscriptions/batch           # 批量获取订阅
GET  /api/v1/health                        # 健康检查
```

### 订阅变更消息格式
```json
{
  "eventId": "sub_event_1234567890_1234",
  "eventType": "CREATE",
  "subscriptionId": "subscription-123",
  "subscription": { ... },
  "eventTimestamp": "2025-07-17T14:30:00",
  "eventSource": "alarm-service",
  "operatorUserId": "admin",
  "operatorUsername": "管理员",
  "description": "创建订阅: 高危告警订阅"
}
```

## 测试验证

### 已创建的测试
- **NotificationArchitectureTest.java**：新架构的完整测试套件
- 包含订阅创建、规则匹配、通知处理、事件更新等测试用例
- 验证了模型的正确性和功能的完整性

### 测试覆盖
- 通知订阅创建和验证
- 规则匹配逻辑测试
- 告警通知处理流程
- 订阅更新事件处理
- 批量订阅更新
- 通知渠道验证
- 统计功能测试

## 后续工作

### 1. services/alarm 模块实现
- 实现订阅管理的 REST API
- 数据库表设计和 ORM 映射
- 用户权限和访问控制
- 订阅变更事件发送

### 2. 消息队列集成
- 配置 Kafka 主题用于订阅变更通知
- 实现消息序列化和反序列化
- 确保消息的可靠传递

### 3. 实际通知渠道集成
- 集成真实的邮件服务（SMTP）
- 集成短信服务提供商 API
- 集成钉钉、企业微信、飞书 Webhook
- 实现通知发送的重试和容错

### 4. 监控和运维
- 添加通知发送的监控指标
- 实现通知失败的告警机制
- 性能监控和调优
- 日志记录和问题排查

### 5. 前端界面
- 订阅管理界面
- 规则配置界面
- 通知历史查看
- 统计报表展示

## 架构优势

### 1. 职责清晰
- **services/alarm**：专注订阅管理
- **alarm-processor**：专注通知推送
- 各模块职责明确，便于维护和扩展

### 2. 可扩展性
- 新增通知渠道只需在 alarm-processor 中实现
- 订阅规则可以灵活扩展
- 支持多种模板引擎

### 3. 高可用性
- Broadcast State 确保配置一致性
- 支持故障恢复和状态重建
- 异步处理提高系统吞吐量

### 4. 易于运维
- 配置集中管理
- 详细的监控和统计
- 完善的日志记录

## 总结

新的架构设计完全符合您提出的要求：

1. **明确分工**：services/alarm 负责订阅管理，alarm-processor 负责推送执行
2. **接口调用**：alarm-processor 通过 HTTP 接口从 services/alarm 获取订阅信息
3. **动态更新**：使用 Broadcast State 模式实现订阅配置的动态更新
4. **消息通知**：services/alarm 变更时通过 Kafka 通知 alarm-processor

这种架构设计更加合理，符合微服务的设计原则，为 NTA 3.0 系统提供了强大而灵活的告警通知能力。
