package com.geeksec.alarmprocessor.notification;

import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.model.AlarmEvent;
import com.geeksec.alarmprocessor.notification.broadcast.SubscriptionUpdateEvent;
import com.geeksec.alarmprocessor.notification.manager.NotificationManager;
import com.geeksec.alarmprocessor.notification.model.NotificationChannel;
import com.geeksec.alarmprocessor.notification.model.NotificationRule;
import com.geeksec.alarmprocessor.notification.model.NotificationSubscription;
import com.geeksec.alarmprocessor.notification.sender.NotificationSender;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 新通知架构测试
 * 
 * <AUTHOR>
 */
public class NotificationArchitectureTest {
    
    private static final Logger log = LoggerFactory.getLogger(NotificationArchitectureTest.class);
    
    private NotificationManager notificationManager;
    private NotificationSender notificationSender;
    
    @BeforeEach
    public void setUp() {
        notificationSender = new NotificationSender();
        notificationManager = new NotificationManager(notificationSender);
    }
    
    @Test
    public void testNotificationSubscriptionCreation() {
        log.info("测试通知订阅创建");
        
        // 创建订阅
        NotificationSubscription subscription = NotificationSubscription.builder()
                .subscriptionId("test-notification-subscription")
                .userId("test-user")
                .username("测试用户")
                .subscriptionName("测试通知订阅")
                .enabled(true)
                .priority(NotificationSubscription.SubscriptionPriority.HIGH)
                .build();
        
        // 添加规则
        NotificationRule rule = NotificationRule.createEqualsRule("alarmLevel", AlarmEvent.AlarmLevel.HIGH);
        subscription.setRules(List.of(rule));
        
        // 添加通知渠道
        NotificationChannel channel = NotificationChannel.createEmailChannel("<EMAIL>", "default_email");
        subscription.setChannels(List.of(channel));
        
        // 验证订阅有效性
        assertTrue(subscription.isValid(), "订阅应该是有效的");
        
        // 添加到管理器
        notificationManager.addSubscription(subscription);
        
        log.info("通知订阅创建测试通过");
    }
    
    @Test
    public void testNotificationRuleMatching() {
        log.info("测试通知规则匹配");
        
        // 创建订阅
        NotificationSubscription subscription = createTestNotificationSubscription();
        notificationManager.addSubscription(subscription);
        
        // 创建匹配的告警
        Alarm matchingAlarm = createTestAlarm("HIGH", "恶意软件");
        
        // 测试匹配
        boolean matches = subscription.matches(matchingAlarm);
        assertTrue(matches, "告警应该匹配订阅规则");
        
        // 创建不匹配的告警
        Alarm nonMatchingAlarm = createTestAlarm("LOW", "正常流量");
        
        // 测试不匹配
        assertFalse(subscription.matches(nonMatchingAlarm), "告警不应该匹配订阅规则");
        
        log.info("通知规则匹配测试通过");
    }
    
    @Test
    public void testAlarmNotificationProcessing() {
        log.info("测试告警通知处理");
        
        // 创建订阅
        NotificationSubscription subscription = createTestNotificationSubscription();
        notificationManager.addSubscription(subscription);
        
        // 创建告警
        Alarm alarm = createTestAlarm("HIGH", "恶意软件");
        
        // 处理告警通知
        NotificationManager.NotificationProcessResult result = 
                notificationManager.processAlarmNotification(alarm);
        
        // 验证处理结果
        assertTrue(result.isSuccess(), "告警通知处理应该成功");
        assertEquals(1, result.getMatchedCount(), "应该匹配1个订阅");
        assertFalse(result.getNotificationResults().isEmpty(), "应该有通知结果");
        
        log.info("告警通知处理测试通过");
    }
    
    @Test
    public void testSubscriptionUpdateEvent() {
        log.info("测试订阅更新事件");
        
        // 创建订阅
        NotificationSubscription subscription = createTestNotificationSubscription();
        
        // 创建订阅创建事件
        SubscriptionUpdateEvent createEvent = SubscriptionUpdateEvent.createSubscriptionCreated(
                subscription, "admin", "管理员");
        
        // 验证事件
        assertTrue(createEvent.isValid(), "创建事件应该是有效的");
        assertEquals(SubscriptionUpdateEvent.EventType.CREATE, createEvent.getEventType());
        assertEquals(subscription.getSubscriptionId(), createEvent.getSubscriptionId());
        
        // 创建订阅更新事件
        SubscriptionUpdateEvent updateEvent = SubscriptionUpdateEvent.createSubscriptionUpdated(
                subscription, "admin", "管理员");
        
        // 验证事件
        assertTrue(updateEvent.isValid(), "更新事件应该是有效的");
        assertEquals(SubscriptionUpdateEvent.EventType.UPDATE, updateEvent.getEventType());
        
        // 创建订阅删除事件
        SubscriptionUpdateEvent deleteEvent = SubscriptionUpdateEvent.createSubscriptionDeleted(
                subscription.getSubscriptionId(), "admin", "管理员");
        
        // 验证事件
        assertTrue(deleteEvent.isValid(), "删除事件应该是有效的");
        assertEquals(SubscriptionUpdateEvent.EventType.DELETE, deleteEvent.getEventType());
        
        log.info("订阅更新事件测试通过");
    }
    
    @Test
    public void testBatchSubscriptionUpdate() {
        log.info("测试批量订阅更新");
        
        // 创建多个订阅
        Map<String, NotificationSubscription> subscriptions = Map.of(
                "sub1", createTestNotificationSubscription("sub1", "user1"),
                "sub2", createTestNotificationSubscription("sub2", "user2"),
                "sub3", createTestNotificationSubscription("sub3", "user3")
        );
        
        // 创建批量更新事件
        SubscriptionUpdateEvent batchUpdateEvent = SubscriptionUpdateEvent.createBatchUpdate(
                subscriptions, "admin", "管理员");
        
        // 验证事件
        assertTrue(batchUpdateEvent.isValid(), "批量更新事件应该是有效的");
        assertEquals(SubscriptionUpdateEvent.EventType.BATCH_UPDATE, batchUpdateEvent.getEventType());
        assertEquals(3, batchUpdateEvent.getSubscriptions().size());
        
        // 更新通知管理器
        notificationManager.updateSubscriptions(subscriptions);
        
        // 验证统计信息
        NotificationManager.NotificationStatistics stats = notificationManager.getStatistics();
        assertEquals(3, stats.totalSubscriptions(), "应该有3个订阅");
        
        log.info("批量订阅更新测试通过");
    }
    
    @Test
    public void testNotificationChannelValidation() {
        log.info("测试通知渠道验证");
        
        // 测试有效的邮件渠道
        NotificationChannel emailChannel = NotificationChannel.createEmailChannel("<EMAIL>", "default_email");
        assertTrue(emailChannel.isAvailable(), "有效的邮件渠道应该可用");
        
        // 测试无效的邮件渠道
        NotificationChannel invalidEmailChannel = NotificationChannel.createEmailChannel("invalid-email", "default_email");
        assertFalse(invalidEmailChannel.isAvailable(), "无效的邮件渠道不应该可用");
        
        // 测试有效的短信渠道
        NotificationChannel smsChannel = NotificationChannel.createSmsChannel("13800138000", "default_sms");
        assertTrue(smsChannel.isAvailable(), "有效的短信渠道应该可用");
        
        // 测试无效的短信渠道
        NotificationChannel invalidSmsChannel = NotificationChannel.createSmsChannel("invalid-phone", "default_sms");
        assertFalse(invalidSmsChannel.isAvailable(), "无效的短信渠道不应该可用");
        
        log.info("通知渠道验证测试通过");
    }
    
    @Test
    public void testNotificationStatistics() {
        log.info("测试通知统计");
        
        // 创建多个订阅
        for (int i = 0; i < 3; i++) {
            NotificationSubscription subscription = createTestNotificationSubscription("subscription-" + i, "user-" + i);
            notificationManager.addSubscription(subscription);
        }
        
        // 获取统计信息
        NotificationManager.NotificationStatistics stats = notificationManager.getStatistics();
        
        // 验证统计信息
        assertEquals(3, stats.totalSubscriptions(), "应该有3个订阅");
        assertTrue(stats.totalTemplates() > 0, "应该有模板");
        
        log.info("通知统计测试通过");
        log.info("统计信息: 订阅数={}, 模板数={}", 
                stats.totalSubscriptions(), stats.totalTemplates());
    }
    
    /**
     * 创建测试通知订阅
     */
    private NotificationSubscription createTestNotificationSubscription() {
        return createTestNotificationSubscription("test-subscription", "test-user");
    }
    
    /**
     * 创建测试通知订阅
     */
    private NotificationSubscription createTestNotificationSubscription(String subscriptionId, String userId) {
        NotificationSubscription subscription = NotificationSubscription.builder()
                .subscriptionId(subscriptionId)
                .userId(userId)
                .username("测试用户" + userId)
                .subscriptionName("测试通知订阅" + subscriptionId)
                .enabled(true)
                .priority(NotificationSubscription.SubscriptionPriority.HIGH)
                .build();
        
        // 添加规则：告警级别为HIGH
        NotificationRule rule = NotificationRule.createEqualsRule("alarmLevel", AlarmEvent.AlarmLevel.HIGH);
        subscription.setRules(List.of(rule));
        
        // 添加通知渠道
        NotificationChannel channel = NotificationChannel.createEmailChannel(userId + "@example.com", "default_email");
        subscription.setChannels(List.of(channel));
        
        return subscription;
    }
    
    /**
     * 创建测试告警
     */
    private Alarm createTestAlarm(String alarmLevel, String threatType) {
        return Alarm.builder()
                .alarmId("test-alarm-" + System.currentTimeMillis())
                .alarmType("威胁检测")
                .alarmName("测试告警")
                .alarmLevel(AlarmEvent.AlarmLevel.valueOf(alarmLevel))
                .threatType(threatType)
                .srcIp("*************")
                .dstIp("********")
                .protocol("TCP")
                .eventTimestamp(LocalDateTime.now())
                .processedTimestamp(LocalDateTime.now())
                .confidence(0.95)
                .description("这是一个测试告警")
                .build();
    }
}
