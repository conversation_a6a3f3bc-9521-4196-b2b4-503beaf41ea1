package com.geeksec.alarmprocessor.notification.source;

import com.geeksec.alarmprocessor.notification.broadcast.SubscriptionUpdateEvent;
import com.geeksec.alarmprocessor.notification.client.AlarmServiceClient;
import com.geeksec.alarmprocessor.notification.model.NotificationSubscription;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订阅更新数据源
 * 负责从告警服务获取订阅配置并生成更新事件
 * 
 * <AUTHOR>
 */
@Slf4j
public class SubscriptionUpdateSource extends RichSourceFunction<SubscriptionUpdateEvent> {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 告警服务客户端
     */
    private final AlarmServiceClient alarmServiceClient;
    
    /**
     * 刷新间隔（秒）
     */
    private final int refreshIntervalSeconds;
    
    /**
     * 是否启用定期刷新
     */
    private final boolean periodicRefreshEnabled;
    
    /**
     * 运行标志
     */
    private volatile boolean running = false;
    
    /**
     * 定时任务执行器
     */
    private transient ScheduledExecutorService scheduledExecutor;
    
    /**
     * 构造函数
     * 
     * @param alarmServiceClient 告警服务客户端
     * @param refreshIntervalSeconds 刷新间隔（秒）
     * @param periodicRefreshEnabled 是否启用定期刷新
     */
    public SubscriptionUpdateSource(AlarmServiceClient alarmServiceClient, 
                                   int refreshIntervalSeconds, 
                                   boolean periodicRefreshEnabled) {
        this.alarmServiceClient = alarmServiceClient;
        this.refreshIntervalSeconds = refreshIntervalSeconds;
        this.periodicRefreshEnabled = periodicRefreshEnabled;
    }
    
    @Override
    public void open(org.apache.flink.configuration.Configuration parameters) throws Exception {
        super.open(parameters);
        
        this.running = true;
        
        if (periodicRefreshEnabled) {
            // 启动定时任务执行器
            scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
                Thread t = new Thread(r, "SubscriptionUpdate-Timer");
                t.setDaemon(true);
                return t;
            });
        }
        
        log.info("订阅更新数据源初始化完成，定期刷新: {}, 刷新间隔: {}秒", 
                periodicRefreshEnabled, refreshIntervalSeconds);
    }
    
    @Override
    public void run(SourceContext<SubscriptionUpdateEvent> ctx) throws Exception {
        // 首次启动时进行全量刷新
        performFullRefresh(ctx);
        
        if (periodicRefreshEnabled && scheduledExecutor != null) {
            // 启动定期刷新任务
            scheduledExecutor.scheduleAtFixedRate(
                    () -> {
                        try {
                            if (running) {
                                performFullRefresh(ctx);
                            }
                        } catch (Exception e) {
                            log.error("定期刷新订阅配置失败: {}", e.getMessage(), e);
                        }
                    },
                    refreshIntervalSeconds,
                    refreshIntervalSeconds,
                    TimeUnit.SECONDS
            );
            
            log.info("启动定期刷新任务，间隔: {}秒", refreshIntervalSeconds);
        }
        
        // 保持数据源运行
        while (running) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    /**
     * 执行全量刷新
     * 
     * @param ctx 数据源上下文
     */
    private void performFullRefresh(SourceContext<SubscriptionUpdateEvent> ctx) {
        try {
            log.debug("开始全量刷新订阅配置");
            
            // 检查告警服务健康状态
            boolean healthy = alarmServiceClient.checkHealth().get(5, TimeUnit.SECONDS);
            if (!healthy) {
                log.warn("告警服务不健康，跳过本次刷新");
                return;
            }
            
            // 获取所有有效订阅
            List<NotificationSubscription> subscriptions = 
                    alarmServiceClient.getAllActiveSubscriptions().get(10, TimeUnit.SECONDS);
            
            if (subscriptions == null || subscriptions.isEmpty()) {
                log.debug("未获取到有效订阅配置");
                return;
            }
            
            // 转换为Map
            Map<String, NotificationSubscription> subscriptionMap = subscriptions.stream()
                    .filter(NotificationSubscription::isValid)
                    .collect(Collectors.toMap(
                            NotificationSubscription::getSubscriptionId,
                            subscription -> subscription,
                            (existing, replacement) -> replacement // 如果有重复，使用新的
                    ));
            
            // 生成全量刷新事件
            SubscriptionUpdateEvent fullRefreshEvent = 
                    SubscriptionUpdateEvent.createFullRefresh(subscriptionMap);
            
            // 发送事件
            synchronized (ctx.getCheckpointLock()) {
                ctx.collect(fullRefreshEvent);
            }
            
            log.info("全量刷新订阅配置完成，共 {} 个有效订阅", subscriptionMap.size());
            
        } catch (Exception e) {
            log.error("全量刷新订阅配置失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理单个订阅更新
     * 
     * @param ctx 数据源上下文
     * @param subscription 订阅信息
     * @param eventType 事件类型
     * @param operatorUserId 操作用户ID
     * @param operatorUsername 操作用户名
     */
    public void handleSubscriptionUpdate(SourceContext<SubscriptionUpdateEvent> ctx,
                                       NotificationSubscription subscription,
                                       SubscriptionUpdateEvent.EventType eventType,
                                       String operatorUserId,
                                       String operatorUsername) {
        try {
            SubscriptionUpdateEvent updateEvent;
            
            switch (eventType) {
                case CREATE -> updateEvent = SubscriptionUpdateEvent.createSubscriptionCreated(
                        subscription, operatorUserId, operatorUsername);
                case UPDATE -> updateEvent = SubscriptionUpdateEvent.createSubscriptionUpdated(
                        subscription, operatorUserId, operatorUsername);
                case DELETE -> updateEvent = SubscriptionUpdateEvent.createSubscriptionDeleted(
                        subscription.getSubscriptionId(), operatorUserId, operatorUsername);
                case ENABLE -> updateEvent = SubscriptionUpdateEvent.createSubscriptionEnabled(
                        subscription.getSubscriptionId(), operatorUserId, operatorUsername);
                case DISABLE -> updateEvent = SubscriptionUpdateEvent.createSubscriptionDisabled(
                        subscription.getSubscriptionId(), operatorUserId, operatorUsername);
                default -> {
                    log.warn("不支持的事件类型: {}", eventType);
                    return;
                }
            }
            
            // 发送事件
            synchronized (ctx.getCheckpointLock()) {
                ctx.collect(updateEvent);
            }
            
            log.info("处理订阅更新事件: 类型={}, 订阅ID={}", 
                    eventType, subscription.getSubscriptionId());
            
        } catch (Exception e) {
            log.error("处理订阅更新失败: 订阅ID={}, 事件类型={}, 错误={}", 
                    subscription.getSubscriptionId(), eventType, e.getMessage(), e);
        }
    }
    
    /**
     * 处理批量订阅更新
     * 
     * @param ctx 数据源上下文
     * @param subscriptions 订阅信息映射
     * @param operatorUserId 操作用户ID
     * @param operatorUsername 操作用户名
     */
    public void handleBatchSubscriptionUpdate(SourceContext<SubscriptionUpdateEvent> ctx,
                                            Map<String, NotificationSubscription> subscriptions,
                                            String operatorUserId,
                                            String operatorUsername) {
        try {
            SubscriptionUpdateEvent batchUpdateEvent = 
                    SubscriptionUpdateEvent.createBatchUpdate(subscriptions, operatorUserId, operatorUsername);
            
            // 发送事件
            synchronized (ctx.getCheckpointLock()) {
                ctx.collect(batchUpdateEvent);
            }
            
            log.info("处理批量订阅更新事件，共 {} 个订阅", subscriptions.size());
            
        } catch (Exception e) {
            log.error("处理批量订阅更新失败: 订阅数量={}, 错误={}", 
                    subscriptions.size(), e.getMessage(), e);
        }
    }
    
    @Override
    public void cancel() {
        log.info("取消订阅更新数据源");
        
        this.running = false;
        
        if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    @Override
    public void close() throws Exception {
        try {
            log.info("关闭订阅更新数据源");
            cancel();
        } finally {
            super.close();
        }
    }
    
    /**
     * 创建默认的订阅更新数据源
     * 
     * @param alarmServiceBaseUrl 告警服务基础URL
     * @return 订阅更新数据源
     */
    public static SubscriptionUpdateSource createDefault(String alarmServiceBaseUrl) {
        AlarmServiceClient.ClientConfig clientConfig = AlarmServiceClient.ClientConfig.defaultConfig();
        AlarmServiceClient alarmServiceClient = new AlarmServiceClient(alarmServiceBaseUrl, clientConfig);
        
        return new SubscriptionUpdateSource(alarmServiceClient, 300, true); // 5分钟刷新一次
    }
    
    /**
     * 创建快速刷新的订阅更新数据源
     * 
     * @param alarmServiceBaseUrl 告警服务基础URL
     * @param refreshIntervalSeconds 刷新间隔（秒）
     * @return 订阅更新数据源
     */
    public static SubscriptionUpdateSource createFastRefresh(String alarmServiceBaseUrl, int refreshIntervalSeconds) {
        AlarmServiceClient.ClientConfig clientConfig = AlarmServiceClient.ClientConfig.fastConfig();
        AlarmServiceClient alarmServiceClient = new AlarmServiceClient(alarmServiceBaseUrl, clientConfig);
        
        return new SubscriptionUpdateSource(alarmServiceClient, refreshIntervalSeconds, true);
    }
    
    /**
     * 创建仅手动刷新的订阅更新数据源
     * 
     * @param alarmServiceBaseUrl 告警服务基础URL
     * @return 订阅更新数据源
     */
    public static SubscriptionUpdateSource createManualRefresh(String alarmServiceBaseUrl) {
        AlarmServiceClient.ClientConfig clientConfig = AlarmServiceClient.ClientConfig.defaultConfig();
        AlarmServiceClient alarmServiceClient = new AlarmServiceClient(alarmServiceBaseUrl, clientConfig);
        
        return new SubscriptionUpdateSource(alarmServiceClient, 0, false); // 不启用定期刷新
    }
}
