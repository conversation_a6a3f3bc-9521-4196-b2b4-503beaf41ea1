package com.geeksec.alarmprocessor.notification.broadcast;

import com.geeksec.alarmprocessor.notification.model.NotificationSubscription;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 订阅更新事件
 * 用于在 Broadcast State 中传递订阅配置变更信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionUpdateEvent implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 事件ID
     */
    private String eventId;
    
    /**
     * 事件类型
     */
    private EventType eventType;
    
    /**
     * 订阅ID
     */
    private String subscriptionId;
    
    /**
     * 订阅信息（用于CREATE和UPDATE事件）
     */
    private NotificationSubscription subscription;
    
    /**
     * 批量订阅信息（用于BATCH_UPDATE事件）
     */
    private Map<String, NotificationSubscription> subscriptions;
    
    /**
     * 事件时间戳
     */
    private LocalDateTime eventTimestamp;
    
    /**
     * 事件来源
     */
    private String eventSource;
    
    /**
     * 操作用户
     */
    private String operatorUserId;
    
    /**
     * 操作用户名
     */
    private String operatorUsername;
    
    /**
     * 事件描述
     */
    private String description;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> properties;
    
    /**
     * 事件类型枚举
     */
    public enum EventType {
        /** 创建订阅 */
        CREATE("create", "创建订阅"),
        /** 更新订阅 */
        UPDATE("update", "更新订阅"),
        /** 删除订阅 */
        DELETE("delete", "删除订阅"),
        /** 批量更新订阅 */
        BATCH_UPDATE("batch_update", "批量更新订阅"),
        /** 启用订阅 */
        ENABLE("enable", "启用订阅"),
        /** 禁用订阅 */
        DISABLE("disable", "禁用订阅"),
        /** 全量刷新 */
        FULL_REFRESH("full_refresh", "全量刷新");
        
        private final String code;
        private final String displayName;
        
        EventType(String code, String displayName) {
            this.code = code;
            this.displayName = displayName;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public static EventType fromCode(String code) {
            for (EventType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
    
    /**
     * 创建订阅创建事件
     * 
     * @param subscription 订阅信息
     * @param operatorUserId 操作用户ID
     * @param operatorUsername 操作用户名
     * @return 订阅更新事件
     */
    public static SubscriptionUpdateEvent createSubscriptionCreated(
            NotificationSubscription subscription, 
            String operatorUserId, 
            String operatorUsername) {
        
        return SubscriptionUpdateEvent.builder()
                .eventId(generateEventId())
                .eventType(EventType.CREATE)
                .subscriptionId(subscription.getSubscriptionId())
                .subscription(subscription)
                .eventTimestamp(LocalDateTime.now())
                .eventSource("alarm-service")
                .operatorUserId(operatorUserId)
                .operatorUsername(operatorUsername)
                .description("创建订阅: " + subscription.getSubscriptionName())
                .build();
    }
    
    /**
     * 创建订阅更新事件
     * 
     * @param subscription 订阅信息
     * @param operatorUserId 操作用户ID
     * @param operatorUsername 操作用户名
     * @return 订阅更新事件
     */
    public static SubscriptionUpdateEvent createSubscriptionUpdated(
            NotificationSubscription subscription, 
            String operatorUserId, 
            String operatorUsername) {
        
        return SubscriptionUpdateEvent.builder()
                .eventId(generateEventId())
                .eventType(EventType.UPDATE)
                .subscriptionId(subscription.getSubscriptionId())
                .subscription(subscription)
                .eventTimestamp(LocalDateTime.now())
                .eventSource("alarm-service")
                .operatorUserId(operatorUserId)
                .operatorUsername(operatorUsername)
                .description("更新订阅: " + subscription.getSubscriptionName())
                .build();
    }
    
    /**
     * 创建订阅删除事件
     * 
     * @param subscriptionId 订阅ID
     * @param operatorUserId 操作用户ID
     * @param operatorUsername 操作用户名
     * @return 订阅更新事件
     */
    public static SubscriptionUpdateEvent createSubscriptionDeleted(
            String subscriptionId, 
            String operatorUserId, 
            String operatorUsername) {
        
        return SubscriptionUpdateEvent.builder()
                .eventId(generateEventId())
                .eventType(EventType.DELETE)
                .subscriptionId(subscriptionId)
                .eventTimestamp(LocalDateTime.now())
                .eventSource("alarm-service")
                .operatorUserId(operatorUserId)
                .operatorUsername(operatorUsername)
                .description("删除订阅: " + subscriptionId)
                .build();
    }
    
    /**
     * 创建批量更新事件
     * 
     * @param subscriptions 订阅信息映射
     * @param operatorUserId 操作用户ID
     * @param operatorUsername 操作用户名
     * @return 订阅更新事件
     */
    public static SubscriptionUpdateEvent createBatchUpdate(
            Map<String, NotificationSubscription> subscriptions, 
            String operatorUserId, 
            String operatorUsername) {
        
        return SubscriptionUpdateEvent.builder()
                .eventId(generateEventId())
                .eventType(EventType.BATCH_UPDATE)
                .subscriptions(subscriptions)
                .eventTimestamp(LocalDateTime.now())
                .eventSource("alarm-service")
                .operatorUserId(operatorUserId)
                .operatorUsername(operatorUsername)
                .description("批量更新订阅，共 " + subscriptions.size() + " 个")
                .build();
    }
    
    /**
     * 创建全量刷新事件
     * 
     * @param subscriptions 所有订阅信息
     * @return 订阅更新事件
     */
    public static SubscriptionUpdateEvent createFullRefresh(Map<String, NotificationSubscription> subscriptions) {
        return SubscriptionUpdateEvent.builder()
                .eventId(generateEventId())
                .eventType(EventType.FULL_REFRESH)
                .subscriptions(subscriptions)
                .eventTimestamp(LocalDateTime.now())
                .eventSource("alarm-processor")
                .operatorUserId("system")
                .operatorUsername("系统")
                .description("全量刷新订阅配置，共 " + subscriptions.size() + " 个")
                .build();
    }
    
    /**
     * 创建订阅启用事件
     * 
     * @param subscriptionId 订阅ID
     * @param operatorUserId 操作用户ID
     * @param operatorUsername 操作用户名
     * @return 订阅更新事件
     */
    public static SubscriptionUpdateEvent createSubscriptionEnabled(
            String subscriptionId, 
            String operatorUserId, 
            String operatorUsername) {
        
        return SubscriptionUpdateEvent.builder()
                .eventId(generateEventId())
                .eventType(EventType.ENABLE)
                .subscriptionId(subscriptionId)
                .eventTimestamp(LocalDateTime.now())
                .eventSource("alarm-service")
                .operatorUserId(operatorUserId)
                .operatorUsername(operatorUsername)
                .description("启用订阅: " + subscriptionId)
                .build();
    }
    
    /**
     * 创建订阅禁用事件
     * 
     * @param subscriptionId 订阅ID
     * @param operatorUserId 操作用户ID
     * @param operatorUsername 操作用户名
     * @return 订阅更新事件
     */
    public static SubscriptionUpdateEvent createSubscriptionDisabled(
            String subscriptionId, 
            String operatorUserId, 
            String operatorUsername) {
        
        return SubscriptionUpdateEvent.builder()
                .eventId(generateEventId())
                .eventType(EventType.DISABLE)
                .subscriptionId(subscriptionId)
                .eventTimestamp(LocalDateTime.now())
                .eventSource("alarm-service")
                .operatorUserId(operatorUserId)
                .operatorUsername(operatorUsername)
                .description("禁用订阅: " + subscriptionId)
                .build();
    }
    
    /**
     * 生成事件ID
     * 
     * @return 事件ID
     */
    private static String generateEventId() {
        return "sub_event_" + System.currentTimeMillis() + "_" + 
               (int) (Math.random() * 10000);
    }
    
    /**
     * 验证事件是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        if (eventType == null || eventTimestamp == null) {
            return false;
        }
        
        switch (eventType) {
            case CREATE, UPDATE -> {
                return subscriptionId != null && subscription != null && subscription.isValid();
            }
            case DELETE, ENABLE, DISABLE -> {
                return subscriptionId != null && !subscriptionId.trim().isEmpty();
            }
            case BATCH_UPDATE, FULL_REFRESH -> {
                return subscriptions != null && !subscriptions.isEmpty();
            }
            default -> {
                return false;
            }
        }
    }
    
    /**
     * 获取事件的简要描述
     * 
     * @return 简要描述
     */
    public String getBriefDescription() {
        if (description != null && !description.trim().isEmpty()) {
            return description;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(eventType.getDisplayName());
        
        if (subscriptionId != null) {
            sb.append(" - ").append(subscriptionId);
        }
        
        if (subscriptions != null) {
            sb.append(" (").append(subscriptions.size()).append("个)");
        }
        
        return sb.toString();
    }
    
    /**
     * 获取扩展属性
     * 
     * @param key 属性键
     * @return 属性值
     */
    public Object getProperty(String key) {
        if (properties == null) {
            return null;
        }
        return properties.get(key);
    }
    
    /**
     * 设置扩展属性
     * 
     * @param key 属性键
     * @param value 属性值
     */
    public void setProperty(String key, Object value) {
        if (properties == null) {
            properties = new java.util.HashMap<>();
        }
        properties.put(key, value);
    }
    
    @Override
    public String toString() {
        return String.format("SubscriptionUpdateEvent{eventId='%s', eventType=%s, subscriptionId='%s', eventTimestamp=%s, description='%s'}", 
                eventId, eventType, subscriptionId, eventTimestamp, description);
    }
}
