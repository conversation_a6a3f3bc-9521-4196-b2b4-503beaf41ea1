package com.geeksec.alarmprocessor.notification.pipeline;

import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.notification.client.AlarmServiceClient;
import com.geeksec.alarmprocessor.notification.function.NotificationFunction;
import com.geeksec.alarmprocessor.notification.sync.SubscriptionConfigSyncService;
import com.geeksec.nta.alarm.dto.subscription.NotificationResultDto;
import com.geeksec.nta.alarm.dto.subscription.NotificationSubscriptionDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.SourceFunction;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 通知处理流水线
 * 负责处理告警通知的发送和结果记录
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class NotificationPipeline {
    
    private final AlarmProcessorConfig config;
    private final AlarmServiceClient alarmServiceClient;
    private final ScheduledExecutorService scheduler;
    
    public SimplifiedNotificationPipeline(AlarmProcessorConfig config) {
        this.config = config;
        this.alarmServiceClient = new AlarmServiceClient(config.getAlarmServiceBaseUrl());
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "notification-pipeline-scheduler");
            t.setDaemon(true);
            return t;
        });
        
        log.info("通知处理流水线初始化完成");
    }
    
    /**
     * 构建通知处理流水线
     * 
     * @param alarmStream 告警数据流
     * @param env Flink执行环境
     * @return 通知结果数据流
     */
    public DataStream<NotificationResultDto> buildNotificationPipeline(DataStream<Alarm> alarmStream,
                                                                       StreamExecutionEnvironment env) {
        log.info("开始构建通知处理流水线");
        
        // 1. 创建订阅配置广播流
        DataStream<NotificationSubscriptionDto> subscriptionStream = createSubscriptionConfigStream(env);
        BroadcastStream<NotificationSubscriptionDto> subscriptionBroadcastStream = 
                subscriptionStream.broadcast(NotificationFunction.SUBSCRIPTION_STATE_DESCRIPTOR);
        
        // 2. 连接告警流和订阅配置广播流
        SingleOutputStreamOperator<NotificationResultDto> notificationResultStream = alarmStream
                .connect(subscriptionBroadcastStream)
                .process(new NotificationFunction(config))
                .name("notification-processor")
                .setParallelism(config.getNotificationParallelism());
        
        // 3. 处理通知结果（批量上报到 services/alarm）
        notificationResultStream
                .keyBy(result -> result.getSubscriptionId())
                .window(org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows.of(
                        org.apache.flink.streaming.api.windowing.time.Time.seconds(30)))
                .apply(new NotificationResultBatchProcessor(alarmServiceClient))
                .name("notification-result-batch-processor")
                .setParallelism(1);
        
        log.info("通知处理流水线构建完成");
        return notificationResultStream;
    }
    
    /**
     * 创建订阅配置数据流
     */
    private DataStream<NotificationSubscriptionDto> createSubscriptionConfigStream(StreamExecutionEnvironment env) {
        return env.addSource(new SubscriptionConfigSource(alarmServiceClient, config))
                .name("subscription-config-source")
                .setParallelism(1);
    }
    
    /**
     * 订阅配置数据源
     */
    private static class SubscriptionConfigSource implements SourceFunction<NotificationSubscriptionDto> {
        
        private static final long serialVersionUID = 1L;
        
        private final AlarmServiceClient alarmServiceClient;
        private final AlarmProcessorConfig config;
        private volatile boolean running = true;
        
        public SubscriptionConfigSource(AlarmServiceClient alarmServiceClient, AlarmProcessorConfig config) {
            this.alarmServiceClient = alarmServiceClient;
            this.config = config;
        }
        
        @Override
        public void run(SourceContext<NotificationSubscriptionDto> ctx) throws Exception {
            log.info("订阅配置数据源开始运行");
            
            // 初始加载
            loadAndEmitSubscriptions(ctx);
            
            // 定期刷新
            while (running) {
                try {
                    Thread.sleep(config.getSubscriptionRefreshIntervalSeconds() * 1000L);
                    if (running) {
                        loadAndEmitSubscriptions(ctx);
                    }
                } catch (InterruptedException e) {
                    log.info("订阅配置数据源被中断");
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    log.error("订阅配置刷新失败", e);
                }
            }
            
            log.info("订阅配置数据源已停止");
        }
        
        @Override
        public void cancel() {
            log.info("取消订阅配置数据源");
            running = false;
        }
        
        /**
         * 加载并发送订阅配置
         */
        private void loadAndEmitSubscriptions(SourceContext<NotificationSubscriptionDto> ctx) {
            try {
                CompletableFuture<List<NotificationSubscriptionDto>> future = 
                        alarmServiceClient.getAllActiveSubscriptions();
                
                List<NotificationSubscriptionDto> subscriptions = future.get(30, TimeUnit.SECONDS);
                
                if (subscriptions != null) {
                    synchronized (ctx.getCheckpointLock()) {
                        for (NotificationSubscriptionDto subscription : subscriptions) {
                            ctx.collect(subscription);
                        }
                    }
                    log.debug("发送订阅配置数量: {}", subscriptions.size());
                } else {
                    log.warn("获取到空的订阅配置");
                }
                
            } catch (Exception e) {
                log.error("加载订阅配置失败", e);
            }
        }
    }
    
    /**
     * 通知结果批量处理器
     */
    private static class NotificationResultBatchProcessor 
            implements org.apache.flink.streaming.api.functions.windowing.WindowFunction<
                    NotificationResultDto, 
                    String, 
                    String, 
                    org.apache.flink.streaming.api.windowing.windows.TimeWindow> {
        
        private static final long serialVersionUID = 1L;
        
        private final AlarmServiceClient alarmServiceClient;
        
        public NotificationResultBatchProcessor(AlarmServiceClient alarmServiceClient) {
            this.alarmServiceClient = alarmServiceClient;
        }
        
        @Override
        public void apply(String subscriptionId,
                         org.apache.flink.streaming.api.windowing.windows.TimeWindow window,
                         Iterable<NotificationResultDto> input,
                         org.apache.flink.util.Collector<String> out) throws Exception {
            
            java.util.List<NotificationResultDto> results = new java.util.ArrayList<>();
            for (NotificationResultDto result : input) {
                results.add(result);
            }
            
            if (!results.isEmpty()) {
                try {
                    CompletableFuture<Boolean> future = alarmServiceClient.recordNotificationResults(results);
                    boolean success = future.get(30, TimeUnit.SECONDS);
                    
                    if (success) {
                        log.debug("批量上报通知结果成功，订阅ID: {}, 数量: {}", subscriptionId, results.size());
                        out.collect("SUCCESS");
                    } else {
                        log.error("批量上报通知结果失败，订阅ID: {}, 数量: {}", subscriptionId, results.size());
                        out.collect("FAILED");
                    }
                    
                } catch (Exception e) {
                    log.error("批量上报通知结果异常，订阅ID: {}, 数量: {}", subscriptionId, results.size(), e);
                    out.collect("ERROR");
                }
            }
        }
    }
    
    /**
     * 关闭资源
     */
    public void close() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("通知处理流水线已关闭");
    }
    
    /**
     * 创建默认通知流水线
     */
    public static NotificationPipeline createDefault(AlarmProcessorConfig config) {
        return new NotificationPipeline(config);
    }
}
