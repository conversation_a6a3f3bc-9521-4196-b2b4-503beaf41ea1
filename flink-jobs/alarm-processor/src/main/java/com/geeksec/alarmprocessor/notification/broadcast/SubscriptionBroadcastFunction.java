package com.geeksec.alarmprocessor.notification.broadcast;

import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.notification.manager.NotificationManager;
import com.geeksec.alarmprocessor.notification.model.NotificationSubscription;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;

import java.util.HashMap;
import java.util.Map;

/**
 * 订阅配置广播处理函数
 * 使用 Broadcast State 模式管理订阅配置的动态更新
 * 
 * <AUTHOR>
 */
@Slf4j
public class SubscriptionBroadcastFunction extends BroadcastProcessFunction<Alarm, SubscriptionUpdateEvent, Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订阅状态描述符
     */
    public static final MapStateDescriptor<String, NotificationSubscription> SUBSCRIPTION_STATE_DESCRIPTOR =
            new MapStateDescriptor<>(
                    "subscription-broadcast-state",
                    String.class,
                    NotificationSubscription.class
            );
    
    /**
     * 通知管理器
     */
    private transient NotificationManager notificationManager;
    
    /**
     * 是否启用透传告警
     */
    private final boolean passthroughAlarms;
    
    /**
     * 统计计数器
     */
    private volatile long processedCount = 0;
    private volatile long matchedCount = 0;
    private volatile long notifiedCount = 0;
    private volatile long failedCount = 0;
    
    /**
     * 构造函数
     * 
     * @param passthroughAlarms 是否透传告警
     */
    public SubscriptionBroadcastFunction(boolean passthroughAlarms) {
        this.passthroughAlarms = passthroughAlarms;
    }
    
    @Override
    public void open(org.apache.flink.configuration.Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化通知管理器
        com.geeksec.alarmprocessor.notification.sender.NotificationSender notificationSender = 
                new com.geeksec.alarmprocessor.notification.sender.NotificationSender();
        notificationManager = new NotificationManager(notificationSender);
        
        log.info("订阅广播处理函数初始化完成");
    }
    
    /**
     * 处理告警流
     * 
     * @param alarm 告警对象
     * @param ctx 上下文
     * @param out 输出收集器
     */
    @Override
    public void processElement(Alarm alarm, ReadOnlyContext ctx, Collector<Alarm> out) throws Exception {
        if (alarm == null) {
            return;
        }
        
        processedCount++;
        
        // 如果启用透传，直接输出原始告警
        if (passthroughAlarms) {
            out.collect(alarm);
        }
        
        try {
            // 获取当前的订阅配置
            ReadOnlyBroadcastState<String, NotificationSubscription> broadcastState = 
                    ctx.getBroadcastState(SUBSCRIPTION_STATE_DESCRIPTOR);
            
            // 更新通知管理器的订阅配置
            Map<String, NotificationSubscription> currentSubscriptions = new HashMap<>();
            for (Map.Entry<String, NotificationSubscription> entry : broadcastState.immutableEntries()) {
                currentSubscriptions.put(entry.getKey(), entry.getValue());
            }
            
            // 如果订阅配置不为空，更新通知管理器
            if (!currentSubscriptions.isEmpty()) {
                notificationManager.updateSubscriptions(currentSubscriptions);
            }
            
            // 处理告警通知
            NotificationManager.NotificationProcessResult result = 
                    notificationManager.processAlarmNotification(alarm);
            
            if (result.isSuccess()) {
                matchedCount += result.getMatchedCount();
                
                // 统计通知结果
                for (NotificationManager.NotificationResult notificationResult : result.getNotificationResults()) {
                    if (notificationResult.isSuccess()) {
                        notifiedCount++;
                    } else {
                        failedCount++;
                    }
                }
                
                log.debug("告警通知处理成功: 告警ID={}, 匹配订阅数={}, 通知结果数={}", 
                        alarm.getAlarmId(), result.getMatchedCount(), result.getNotificationResults().size());
            } else {
                failedCount++;
                log.warn("告警通知处理失败: 告警ID={}, 错误={}", alarm.getAlarmId(), result.getMessage());
            }
            
        } catch (Exception e) {
            failedCount++;
            log.error("处理告警通知时发生异常: 告警ID={}, 错误={}", alarm.getAlarmId(), e.getMessage(), e);
        }
    }
    
    /**
     * 处理订阅更新事件（广播流）
     * 
     * @param updateEvent 订阅更新事件
     * @param ctx 上下文
     * @param out 输出收集器
     */
    @Override
    public void processBroadcastElement(SubscriptionUpdateEvent updateEvent, Context ctx, Collector<Alarm> out) throws Exception {
        if (updateEvent == null) {
            log.warn("接收到空的订阅更新事件");
            return;
        }
        
        log.info("处理订阅更新事件: 类型={}, 订阅ID={}", 
                updateEvent.getEventType(), updateEvent.getSubscriptionId());
        
        try {
            BroadcastState<String, NotificationSubscription> broadcastState = 
                    ctx.getBroadcastState(SUBSCRIPTION_STATE_DESCRIPTOR);
            
            switch (updateEvent.getEventType()) {
                case CREATE, UPDATE -> {
                    NotificationSubscription subscription = updateEvent.getSubscription();
                    if (subscription != null && subscription.isValid()) {
                        broadcastState.put(subscription.getSubscriptionId(), subscription);
                        log.info("更新订阅配置: {}", subscription.getSubscriptionId());
                    } else {
                        log.warn("无效的订阅配置: {}", updateEvent.getSubscriptionId());
                    }
                }
                case DELETE -> {
                    broadcastState.remove(updateEvent.getSubscriptionId());
                    log.info("删除订阅配置: {}", updateEvent.getSubscriptionId());
                }
                case BATCH_UPDATE -> {
                    Map<String, NotificationSubscription> subscriptions = updateEvent.getSubscriptions();
                    if (subscriptions != null && !subscriptions.isEmpty()) {
                        // 清空现有配置
                        broadcastState.clear();
                        
                        // 批量更新
                        for (Map.Entry<String, NotificationSubscription> entry : subscriptions.entrySet()) {
                            if (entry.getValue() != null && entry.getValue().isValid()) {
                                broadcastState.put(entry.getKey(), entry.getValue());
                            }
                        }
                        
                        log.info("批量更新订阅配置完成，共 {} 个订阅", subscriptions.size());
                    }
                }
                default -> {
                    log.warn("未知的订阅更新事件类型: {}", updateEvent.getEventType());
                }
            }
            
        } catch (Exception e) {
            log.error("处理订阅更新事件失败: 事件={}, 错误={}", updateEvent, e.getMessage(), e);
        }
    }
    
    @Override
    public void close() throws Exception {
        try {
            log.info("关闭订阅广播处理函数");
            
            // 打印最终统计信息
            printFinalStatistics();
            
        } finally {
            super.close();
        }
    }
    
    /**
     * 打印最终统计信息
     */
    private void printFinalStatistics() {
        log.info("=== 订阅广播处理最终统计 ===");
        log.info("总处理告警数: {}", processedCount);
        log.info("总匹配订阅数: {}", matchedCount);
        log.info("总成功通知数: {}", notifiedCount);
        log.info("总失败通知数: {}", failedCount);
        
        if (processedCount > 0) {
            double matchRate = (double) matchedCount / processedCount * 100;
            log.info("总匹配率: {:.2f}%", matchRate);
        }
        
        if (matchedCount > 0) {
            double successRate = (double) notifiedCount / (notifiedCount + failedCount) * 100;
            log.info("总通知成功率: {:.2f}%", successRate);
        }
        
        if (notificationManager != null) {
            NotificationManager.NotificationStatistics stats = notificationManager.getStatistics();
            log.info("通知管理器最终统计:");
            log.info("  总订阅数: {}", stats.totalSubscriptions());
            log.info("  总模板数: {}", stats.totalTemplates());
            log.info("  总处理数: {}", stats.totalProcessed());
            log.info("  总匹配数: {}", stats.totalMatched());
            log.info("  总通知数: {}", stats.totalNotified());
            log.info("  总失败数: {}", stats.totalFailed());
            log.info("  匹配率: {:.2f}%", stats.getMatchRate() * 100);
            log.info("  通知成功率: {:.2f}%", stats.getNotificationSuccessRate() * 100);
            log.info("  最后更新时间: {}", stats.lastUpdateTime());
        }
        
        log.info("=== 最终统计完成 ===");
    }
}
