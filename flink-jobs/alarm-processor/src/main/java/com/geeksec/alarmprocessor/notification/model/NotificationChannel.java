package com.geeksec.alarmprocessor.notification.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知渠道模型
 * 定义告警通知的发送渠道
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationChannel implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 渠道ID
     */
    private String channelId;
    
    /**
     * 渠道名称
     */
    private String channelName;
    
    /**
     * 渠道类型
     */
    private ChannelType channelType;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 接收地址（邮箱、手机号、webhook地址等）
     */
    private String address;
    
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * 渠道配置参数
     */
    private Map<String, Object> config;
    
    /**
     * 优先级
     */
    private Integer priority;
    
    /**
     * 超时时间（毫秒）
     */
    private Long timeoutMs;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 重试间隔（毫秒）
     */
    private Long retryIntervalMs;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 最后发送时间
     */
    private LocalDateTime lastSentTime;
    
    /**
     * 发送成功次数
     */
    private Long successCount;
    
    /**
     * 发送失败次数
     */
    private Long failureCount;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> properties;
    
    /**
     * 渠道类型枚举
     */
    public enum ChannelType {
        /** 邮件 */
        EMAIL("email", "邮件"),
        /** 短信 */
        SMS("sms", "短信"),
        /** 钉钉 */
        DINGTALK("dingtalk", "钉钉"),
        /** 企业微信 */
        WECHAT_WORK("wechat_work", "企业微信"),
        /** 飞书 */
        FEISHU("feishu", "飞书"),
        /** Webhook */
        WEBHOOK("webhook", "Webhook"),
        /** Slack */
        SLACK("slack", "Slack"),
        /** Teams */
        TEAMS("teams", "Teams"),
        /** 自定义 */
        CUSTOM("custom", "自定义");
        
        private final String code;
        private final String displayName;
        
        ChannelType(String code, String displayName) {
            this.code = code;
            this.displayName = displayName;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public static ChannelType fromCode(String code) {
            for (ChannelType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
    
    /**
     * 创建邮件渠道
     * 
     * @param email 邮箱地址
     * @param templateId 模板ID
     * @return 渠道实例
     */
    public static NotificationChannel createEmailChannel(String email, String templateId) {
        return NotificationChannel.builder()
                .channelType(ChannelType.EMAIL)
                .address(email)
                .templateId(templateId)
                .enabled(true)
                .priority(1)
                .timeoutMs(30000L)
                .retryCount(3)
                .retryIntervalMs(1000L)
                .successCount(0L)
                .failureCount(0L)
                .createTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建短信渠道
     * 
     * @param phone 手机号
     * @param templateId 模板ID
     * @return 渠道实例
     */
    public static NotificationChannel createSmsChannel(String phone, String templateId) {
        return NotificationChannel.builder()
                .channelType(ChannelType.SMS)
                .address(phone)
                .templateId(templateId)
                .enabled(true)
                .priority(2)
                .timeoutMs(10000L)
                .retryCount(3)
                .retryIntervalMs(1000L)
                .successCount(0L)
                .failureCount(0L)
                .createTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建钉钉渠道
     * 
     * @param webhookUrl Webhook地址
     * @param templateId 模板ID
     * @return 渠道实例
     */
    public static NotificationChannel createDingTalkChannel(String webhookUrl, String templateId) {
        return NotificationChannel.builder()
                .channelType(ChannelType.DINGTALK)
                .address(webhookUrl)
                .templateId(templateId)
                .enabled(true)
                .priority(3)
                .timeoutMs(15000L)
                .retryCount(2)
                .retryIntervalMs(1000L)
                .successCount(0L)
                .failureCount(0L)
                .createTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建企业微信渠道
     * 
     * @param webhookUrl Webhook地址
     * @param templateId 模板ID
     * @return 渠道实例
     */
    public static NotificationChannel createWeChatWorkChannel(String webhookUrl, String templateId) {
        return NotificationChannel.builder()
                .channelType(ChannelType.WECHAT_WORK)
                .address(webhookUrl)
                .templateId(templateId)
                .enabled(true)
                .priority(3)
                .timeoutMs(15000L)
                .retryCount(2)
                .retryIntervalMs(1000L)
                .successCount(0L)
                .failureCount(0L)
                .createTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建飞书渠道
     * 
     * @param webhookUrl Webhook地址
     * @param templateId 模板ID
     * @return 渠道实例
     */
    public static NotificationChannel createFeishuChannel(String webhookUrl, String templateId) {
        return NotificationChannel.builder()
                .channelType(ChannelType.FEISHU)
                .address(webhookUrl)
                .templateId(templateId)
                .enabled(true)
                .priority(3)
                .timeoutMs(15000L)
                .retryCount(2)
                .retryIntervalMs(1000L)
                .successCount(0L)
                .failureCount(0L)
                .createTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 检查渠道是否可用
     * 
     * @return 是否可用
     */
    public boolean isAvailable() {
        if (!Boolean.TRUE.equals(enabled)) {
            return false;
        }
        
        if (channelType == null) {
            return false;
        }
        
        if (address == null || address.trim().isEmpty()) {
            return false;
        }
        
        // 根据渠道类型进行特定的验证
        switch (channelType) {
            case EMAIL -> {
                return isValidEmail(address);
            }
            case SMS -> {
                return isValidPhone(address);
            }
            case DINGTALK, WECHAT_WORK, FEISHU, WEBHOOK -> {
                return isValidUrl(address);
            }
            default -> {
                return true;
            }
        }
    }
    
    /**
     * 验证邮箱地址格式
     */
    private boolean isValidEmail(String email) {
        return email != null && email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }
    
    /**
     * 验证手机号格式
     */
    private boolean isValidPhone(String phone) {
        return phone != null && phone.matches("^1[3-9]\\d{9}$");
    }
    
    /**
     * 验证URL格式
     */
    private boolean isValidUrl(String url) {
        return url != null && (url.startsWith("http://") || url.startsWith("https://"));
    }
    
    /**
     * 更新发送统计信息
     * 
     * @param success 是否发送成功
     */
    public void updateSendStatistics(boolean success) {
        this.lastSentTime = LocalDateTime.now();
        
        if (success) {
            this.successCount = (this.successCount == null ? 0 : this.successCount) + 1;
        } else {
            this.failureCount = (this.failureCount == null ? 0 : this.failureCount) + 1;
        }
        
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 获取发送成功率
     * 
     * @return 成功率（0-1之间）
     */
    public double getSuccessRate() {
        long total = getTotalSendCount();
        if (total == 0) {
            return 0.0;
        }
        
        long success = this.successCount == null ? 0 : this.successCount;
        return (double) success / total;
    }
    
    /**
     * 获取总发送次数
     * 
     * @return 总发送次数
     */
    public long getTotalSendCount() {
        long success = this.successCount == null ? 0 : this.successCount;
        long failure = this.failureCount == null ? 0 : this.failureCount;
        return success + failure;
    }
    
    /**
     * 检查是否为高优先级渠道
     * 
     * @return 是否为高优先级
     */
    public boolean isHighPriority() {
        return priority != null && priority <= 2;
    }
    
    /**
     * 获取渠道的显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        if (channelName != null && !channelName.trim().isEmpty()) {
            return channelName;
        }
        
        if (channelType != null) {
            return channelType.getDisplayName() + " - " + address;
        }
        
        return "未知渠道";
    }
    
    /**
     * 获取渠道的简要描述
     * 
     * @return 简要描述
     */
    public String getBriefDescription() {
        StringBuilder sb = new StringBuilder();
        
        if (channelType != null) {
            sb.append(channelType.getDisplayName());
        }
        
        if (address != null) {
            sb.append(" (").append(address).append(")");
        }
        
        if (Boolean.TRUE.equals(enabled)) {
            sb.append(" [启用]");
        } else {
            sb.append(" [禁用]");
        }
        
        return sb.toString();
    }
    
    /**
     * 获取配置参数
     * 
     * @param key 参数键
     * @return 参数值
     */
    public Object getConfigValue(String key) {
        if (config == null) {
            return null;
        }
        return config.get(key);
    }
    
    /**
     * 设置配置参数
     * 
     * @param key 参数键
     * @param value 参数值
     */
    public void setConfigValue(String key, Object value) {
        if (config == null) {
            config = new java.util.HashMap<>();
        }
        config.put(key, value);
    }
    
    /**
     * 获取扩展属性
     * 
     * @param key 属性键
     * @return 属性值
     */
    public Object getProperty(String key) {
        if (properties == null) {
            return null;
        }
        return properties.get(key);
    }
    
    /**
     * 设置扩展属性
     * 
     * @param key 属性键
     * @param value 属性值
     */
    public void setProperty(String key, Object value) {
        if (properties == null) {
            properties = new java.util.HashMap<>();
        }
        properties.put(key, value);
    }
}
