package com.geeksec.alarmprocessor.notification.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * 通知订阅模型
 * 用于告警推送的订阅信息（从告警管理服务获取）
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationSubscription implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订阅ID
     */
    private String subscriptionId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 订阅名称
     */
    private String subscriptionName;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 匹配规则列表
     */
    private List<NotificationRule> rules;
    
    /**
     * 通知渠道列表
     */
    private List<NotificationChannel> channels;
    
    /**
     * 通知频率控制
     */
    private NotificationFrequency frequency;
    
    /**
     * 免打扰时间设置
     */
    private QuietHours quietHours;
    
    /**
     * 订阅优先级
     */
    private SubscriptionPriority priority;
    
    /**
     * 最后触发时间
     */
    private LocalDateTime lastTriggeredTime;
    
    /**
     * 触发次数
     */
    private Long triggerCount;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> properties;
    
    /**
     * 订阅优先级枚举
     */
    public enum SubscriptionPriority {
        /** 低优先级 */
        LOW(1),
        /** 普通优先级 */
        NORMAL(2),
        /** 高优先级 */
        HIGH(3),
        /** 紧急优先级 */
        URGENT(4);
        
        private final int level;
        
        SubscriptionPriority(int level) {
            this.level = level;
        }
        
        public int getLevel() {
            return level;
        }
    }
    
    /**
     * 通知频率控制
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NotificationFrequency implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 频率类型
         */
        private FrequencyType type;
        
        /**
         * 时间间隔（分钟）
         */
        private Integer intervalMinutes;
        
        /**
         * 最大通知次数（每天）
         */
        private Integer maxNotificationsPerDay;
        
        /**
         * 批量通知阈值
         */
        private Integer batchThreshold;
        
        /**
         * 批量等待时间（分钟）
         */
        private Integer batchWaitMinutes;
        
        /**
         * 频率类型枚举
         */
        public enum FrequencyType {
            /** 实时通知 */
            REAL_TIME,
            /** 间隔通知 */
            INTERVAL,
            /** 批量通知 */
            BATCH,
            /** 摘要通知 */
            DIGEST
        }
    }
    
    /**
     * 免打扰时间设置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuietHours implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 是否启用免打扰
         */
        private Boolean enabled;
        
        /**
         * 开始时间
         */
        private LocalTime startTime;
        
        /**
         * 结束时间
         */
        private LocalTime endTime;
        
        /**
         * 免打扰日期（周几）
         */
        private List<Integer> quietDays;
        
        /**
         * 紧急告警是否忽略免打扰
         */
        private Boolean urgentIgnoreQuiet;
        
        /**
         * 时区
         */
        private String timezone;
    }
    
    /**
     * 验证订阅配置是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        if (subscriptionId == null || subscriptionId.trim().isEmpty()) {
            return false;
        }
        
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }
        
        if (rules == null || rules.isEmpty()) {
            return false;
        }
        
        if (channels == null || channels.isEmpty()) {
            return false;
        }
        
        // 验证规则有效性
        for (NotificationRule rule : rules) {
            if (!rule.isValid()) {
                return false;
            }
        }
        
        // 验证渠道有效性
        for (NotificationChannel channel : channels) {
            if (!channel.isAvailable()) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查是否在免打扰时间内
     * 
     * @return 是否在免打扰时间内
     */
    public boolean isInQuietHours() {
        if (quietHours == null || !Boolean.TRUE.equals(quietHours.getEnabled())) {
            return false;
        }
        
        LocalTime now = LocalTime.now();
        LocalTime start = quietHours.getStartTime();
        LocalTime end = quietHours.getEndTime();
        
        if (start == null || end == null) {
            return false;
        }
        
        // 处理跨天的情况
        if (start.isBefore(end)) {
            return !now.isBefore(start) && !now.isAfter(end);
        } else {
            return !now.isBefore(start) || !now.isAfter(end);
        }
    }
    
    /**
     * 检查是否应该发送通知
     * 
     * @param isUrgent 是否为紧急告警
     * @return 是否应该发送通知
     */
    public boolean shouldSendNotification(boolean isUrgent) {
        // 检查订阅是否启用
        if (!Boolean.TRUE.equals(enabled)) {
            return false;
        }
        
        // 检查免打扰时间
        if (isInQuietHours()) {
            // 如果是紧急告警且设置了忽略免打扰，则发送
            if (isUrgent && quietHours != null && 
                Boolean.TRUE.equals(quietHours.getUrgentIgnoreQuiet())) {
                return true;
            }
            return false;
        }
        
        // 这里可以添加更多的频率控制逻辑
        return true;
    }
    
    /**
     * 检查订阅是否匹配告警
     * 
     * @param alarm 告警对象
     * @return 是否匹配
     */
    public boolean matches(Object alarm) {
        if (rules == null || rules.isEmpty()) {
            return false;
        }
        
        // 所有规则都必须匹配（AND逻辑）
        for (NotificationRule rule : rules) {
            if (!rule.matches(alarm)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取有效的通知渠道
     * 
     * @return 有效的通知渠道列表
     */
    public List<NotificationChannel> getAvailableChannels() {
        if (channels == null) {
            return List.of();
        }
        
        return channels.stream()
                .filter(NotificationChannel::isAvailable)
                .toList();
    }
    
    /**
     * 更新触发信息
     */
    public void updateTriggerInfo() {
        this.lastTriggeredTime = LocalDateTime.now();
        this.triggerCount = (this.triggerCount == null ? 0 : this.triggerCount) + 1;
    }
    
    /**
     * 获取订阅的优先级级别
     * 
     * @return 优先级级别
     */
    public int getPriorityLevel() {
        return priority != null ? priority.getLevel() : SubscriptionPriority.NORMAL.getLevel();
    }
    
    /**
     * 检查是否为高优先级订阅
     * 
     * @return 是否为高优先级
     */
    public boolean isHighPriority() {
        return priority != null && 
               (priority == SubscriptionPriority.HIGH || priority == SubscriptionPriority.URGENT);
    }
    
    /**
     * 检查是否为紧急订阅
     * 
     * @return 是否为紧急订阅
     */
    public boolean isUrgent() {
        return priority == SubscriptionPriority.URGENT;
    }
}
