package com.geeksec.alarmprocessor.subscription.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 订阅配置类
 * 统一管理告警订阅相关的配置参数
 * 
 * <AUTHOR>
 */
@Data
@Slf4j
public class SubscriptionConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // ==================== 基础配置 ====================
    
    /**
     * 是否启用订阅功能
     */
    private boolean enabled = true;
    
    /**
     * 是否透传原始告警
     */
    private boolean passthroughAlarms = true;
    
    /**
     * 订阅处理并行度
     */
    private int parallelism = 2;
    
    /**
     * 最大订阅数量
     */
    private int maxSubscriptions = 10000;
    
    /**
     * 最大用户数量
     */
    private int maxUsers = 1000;
    
    // ==================== 匹配配置 ====================
    
    /**
     * 订阅匹配超时时间（毫秒）
     */
    private long matchTimeoutMs = 5000L;
    
    /**
     * 是否启用订阅缓存
     */
    private boolean subscriptionCacheEnabled = true;
    
    /**
     * 订阅缓存过期时间（毫秒）
     */
    private long subscriptionCacheExpirationMs = 300000L; // 5分钟
    
    /**
     * 订阅缓存最大大小
     */
    private int subscriptionCacheMaxSize = 1000;
    
    /**
     * 是否启用规则缓存
     */
    private boolean ruleCacheEnabled = true;
    
    /**
     * 规则缓存过期时间（毫秒）
     */
    private long ruleCacheExpirationMs = 600000L; // 10分钟
    
    // ==================== 通知配置 ====================
    
    /**
     * 是否启用异步通知
     */
    private boolean asyncNotificationEnabled = true;
    
    /**
     * 通知队列大小
     */
    private int notificationQueueSize = 10000;
    
    /**
     * 通知线程池大小
     */
    private int notificationThreadPoolSize = 10;
    
    /**
     * 通知超时时间（毫秒）
     */
    private long notificationTimeoutMs = 30000L;
    
    /**
     * 通知重试次数
     */
    private int notificationRetryCount = 3;
    
    /**
     * 通知重试间隔（毫秒）
     */
    private long notificationRetryIntervalMs = 1000L;
    
    /**
     * 批量通知大小
     */
    private int batchNotificationSize = 100;
    
    /**
     * 批量通知等待时间（毫秒）
     */
    private long batchNotificationWaitMs = 5000L;
    
    // ==================== 频率控制配置 ====================
    
    /**
     * 是否启用频率控制
     */
    private boolean frequencyControlEnabled = true;
    
    /**
     * 默认频率控制窗口大小（分钟）
     */
    private int defaultFrequencyWindowMinutes = 60;
    
    /**
     * 默认频率控制最大次数
     */
    private int defaultFrequencyMaxCount = 10;
    
    /**
     * 是否启用全局频率控制
     */
    private boolean globalFrequencyControlEnabled = true;
    
    /**
     * 全局频率控制窗口大小（分钟）
     */
    private int globalFrequencyWindowMinutes = 60;
    
    /**
     * 全局频率控制最大次数
     */
    private int globalFrequencyMaxCount = 1000;
    
    // ==================== 模板配置 ====================
    
    /**
     * 是否启用模板缓存
     */
    private boolean templateCacheEnabled = true;
    
    /**
     * 模板缓存过期时间（毫秒）
     */
    private long templateCacheExpirationMs = 1800000L; // 30分钟
    
    /**
     * 模板缓存最大大小
     */
    private int templateCacheMaxSize = 500;
    
    /**
     * 默认模板ID
     */
    private String defaultTemplateId = "default_alarm_template";
    
    /**
     * 模板渲染超时时间（毫秒）
     */
    private long templateRenderTimeoutMs = 5000L;
    
    // ==================== 统计配置 ====================
    
    /**
     * 是否启用统计功能
     */
    private boolean statisticsEnabled = true;
    
    /**
     * 统计输出间隔（秒）
     */
    private int statisticsIntervalSeconds = 300; // 5分钟
    
    /**
     * 是否启用性能监控
     */
    private boolean performanceMonitoringEnabled = true;
    
    /**
     * 性能监控采样率
     */
    private double performanceMonitoringSampleRate = 0.1;
    
    // ==================== 配置刷新 ====================
    
    /**
     * 配置刷新间隔（秒）
     */
    private int configRefreshIntervalSeconds = 300; // 5分钟
    
    /**
     * 配置来源类型
     */
    private ConfigSourceType configSourceType = ConfigSourceType.FILE;
    
    /**
     * 配置文件路径
     */
    private String configFilePath = "subscription-config.json";
    
    /**
     * 配置数据库连接信息
     */
    private DatabaseConfig databaseConfig;
    
    // ==================== 安全配置 ====================
    
    /**
     * 是否启用访问控制
     */
    private boolean accessControlEnabled = true;
    
    /**
     * 允许的IP地址列表
     */
    private List<String> allowedIpAddresses;
    
    /**
     * 允许的用户角色列表
     */
    private List<String> allowedUserRoles;
    
    /**
     * 是否启用通知内容过滤
     */
    private boolean notificationContentFilterEnabled = true;
    
    /**
     * 敏感信息过滤规则
     */
    private List<String> sensitiveInfoFilterRules;
    
    /**
     * 邮件配置
     */
    @Data
    public static class EmailConfig implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean enabled = true;
        private String smtpHost;
        private int smtpPort = 587;
        private String username;
        private String password;
        private boolean tlsEnabled = true;
        private String fromAddress;
        private String fromName = "威胁检测系统";
        private int connectionTimeoutMs = 10000;
        private int readTimeoutMs = 10000;
    }
    
    /**
     * 短信配置
     */
    @Data
    public static class SmsConfig implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean enabled = true;
        private String apiUrl;
        private String accessKey;
        private String secretKey;
        private String signName;
        private String templateCode;
        private int connectionTimeoutMs = 10000;
        private int readTimeoutMs = 10000;
    }
    
    /**
     * 钉钉配置
     */
    @Data
    public static class DingTalkConfig implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean enabled = true;
        private String webhookUrl;
        private String secret;
        private int connectionTimeoutMs = 10000;
        private int readTimeoutMs = 10000;
    }
    
    /**
     * 企业微信配置
     */
    @Data
    public static class WeChatWorkConfig implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean enabled = true;
        private String webhookUrl;
        private int connectionTimeoutMs = 10000;
        private int readTimeoutMs = 10000;
    }
    
    /**
     * 飞书配置
     */
    @Data
    public static class FeishuConfig implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean enabled = true;
        private String webhookUrl;
        private String secret;
        private int connectionTimeoutMs = 10000;
        private int readTimeoutMs = 10000;
    }
    
    /**
     * 数据库配置
     */
    @Data
    public static class DatabaseConfig implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private String url;
        private String username;
        private String password;
        private String driverClassName = "org.postgresql.Driver";
        private int maxPoolSize = 10;
        private int minPoolSize = 2;
        private int connectionTimeoutMs = 30000;
        private int idleTimeoutMs = 600000;
        private int maxLifetimeMs = 1800000;
    }
    
    /**
     * 配置来源类型
     */
    public enum ConfigSourceType {
        /** 文件配置 */
        FILE,
        /** 数据库配置 */
        DATABASE,
        /** 环境变量配置 */
        ENVIRONMENT,
        /** 配置中心 */
        CONFIG_CENTER
    }
    
    /**
     * 验证配置有效性
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        if (maxSubscriptions <= 0 || maxUsers <= 0) {
            return false;
        }
        
        if (matchTimeoutMs <= 0 || notificationTimeoutMs <= 0) {
            return false;
        }
        
        if (parallelism <= 0 || notificationThreadPoolSize <= 0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取邮件配置
     */
    private EmailConfig emailConfig;
    
    /**
     * 获取短信配置
     */
    private SmsConfig smsConfig;
    
    /**
     * 获取钉钉配置
     */
    private DingTalkConfig dingTalkConfig;
    
    /**
     * 获取企业微信配置
     */
    private WeChatWorkConfig weChatWorkConfig;
    
    /**
     * 获取飞书配置
     */
    private FeishuConfig feishuConfig;
    
    /**
     * 打印配置信息
     */
    public void printConfig() {
        log.info("=== 订阅配置信息 ===");
        log.info("基础配置:");
        log.info("  启用状态: {}", enabled);
        log.info("  透传告警: {}", passthroughAlarms);
        log.info("  并行度: {}", parallelism);
        log.info("  最大订阅数: {}", maxSubscriptions);
        
        log.info("匹配配置:");
        log.info("  匹配超时: {}ms", matchTimeoutMs);
        log.info("  缓存启用: {}", subscriptionCacheEnabled);
        log.info("  缓存过期: {}ms", subscriptionCacheExpirationMs);
        
        log.info("通知配置:");
        log.info("  异步通知: {}", asyncNotificationEnabled);
        log.info("  队列大小: {}", notificationQueueSize);
        log.info("  通知超时: {}ms", notificationTimeoutMs);
        log.info("  重试次数: {}", notificationRetryCount);
        
        log.info("频率控制:");
        log.info("  频率控制启用: {}", frequencyControlEnabled);
        log.info("  默认窗口: {}分钟", defaultFrequencyWindowMinutes);
        log.info("  默认最大次数: {}", defaultFrequencyMaxCount);
        
        log.info("统计配置:");
        log.info("  统计启用: {}", statisticsEnabled);
        log.info("  统计间隔: {}秒", statisticsIntervalSeconds);
        log.info("  性能监控: {}", performanceMonitoringEnabled);
        
        log.info("=== 配置信息完成 ===");
    }
}
