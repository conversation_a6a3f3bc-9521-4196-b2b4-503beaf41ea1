package com.geeksec.alarmprocessor.notification.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通知模板模型
 * 定义告警通知的消息模板
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationTemplate implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 模板类型
     */
    private TemplateType templateType;
    
    /**
     * 渠道类型
     */
    private NotificationChannel.ChannelType channelType;
    
    /**
     * 标题模板
     */
    private String titleTemplate;
    
    /**
     * 内容模板
     */
    private String contentTemplate;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 模板语言
     */
    private TemplateLanguage language;
    
    /**
     * 模板版本
     */
    private String version;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建者
     */
    private String creator;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> properties;
    
    /**
     * 模板类型枚举
     */
    public enum TemplateType {
        /** 告警通知 */
        ALARM_NOTIFICATION("alarm_notification", "告警通知"),
        /** 系统通知 */
        SYSTEM_NOTIFICATION("system_notification", "系统通知"),
        /** 摘要报告 */
        SUMMARY_REPORT("summary_report", "摘要报告"),
        /** 自定义 */
        CUSTOM("custom", "自定义");
        
        private final String code;
        private final String displayName;
        
        TemplateType(String code, String displayName) {
            this.code = code;
            this.displayName = displayName;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    /**
     * 模板语言枚举
     */
    public enum TemplateLanguage {
        /** 简单变量替换 */
        SIMPLE("simple", "简单变量替换"),
        /** Freemarker */
        FREEMARKER("freemarker", "Freemarker"),
        /** Velocity */
        VELOCITY("velocity", "Velocity"),
        /** Thymeleaf */
        THYMELEAF("thymeleaf", "Thymeleaf");
        
        private final String code;
        private final String displayName;
        
        TemplateLanguage(String code, String displayName) {
            this.code = code;
            this.displayName = displayName;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    /**
     * 渲染后的模板
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RenderedTemplate implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 渲染后的标题
         */
        private String title;
        
        /**
         * 渲染后的内容
         */
        private String content;
        
        /**
         * 渲染时间
         */
        private LocalDateTime renderTime;
        
        /**
         * 模板ID
         */
        private String templateId;
        
        /**
         * 渲染上下文
         */
        private Map<String, Object> context;
    }
    
    /**
     * 创建默认告警模板
     * 
     * @param channelType 渠道类型
     * @return 模板实例
     */
    public static NotificationTemplate createDefaultAlarmTemplate(NotificationChannel.ChannelType channelType) {
        String titleTemplate;
        String contentTemplate;
        
        switch (channelType) {
            case EMAIL -> {
                titleTemplate = "【告警通知】${alarmType} - ${alarmName}";
                contentTemplate = """
                    <h3>告警详情</h3>
                    <p><strong>告警类型：</strong>${alarmType}</p>
                    <p><strong>告警名称：</strong>${alarmName}</p>
                    <p><strong>告警级别：</strong>${alarmLevel}</p>
                    <p><strong>威胁类型：</strong>${threatType}</p>
                    <p><strong>源IP：</strong>${srcIp}</p>
                    <p><strong>目标IP：</strong>${dstIp}</p>
                    <p><strong>协议：</strong>${protocol}</p>
                    <p><strong>发生时间：</strong>${eventTimestamp}</p>
                    <p><strong>处理时间：</strong>${processedTimestamp}</p>
                    <p><strong>置信度：</strong>${confidence}</p>
                    <p><strong>风险评分：</strong>${riskScore}</p>
                    <p><strong>描述：</strong>${description}</p>
                    """;
            }
            case SMS -> {
                titleTemplate = "告警：${alarmType}";
                contentTemplate = "【告警】${alarmType}-${alarmName}，源IP：${srcIp}，目标IP：${dstIp}，时间：${eventTimestamp}";
            }
            case DINGTALK, WECHAT_WORK, FEISHU -> {
                titleTemplate = "【告警通知】${alarmType} - ${alarmName}";
                contentTemplate = """
                    ## 告警详情
                    
                    **告警类型：** ${alarmType}
                    
                    **告警名称：** ${alarmName}
                    
                    **告警级别：** ${alarmLevel}
                    
                    **威胁类型：** ${threatType}
                    
                    **源IP：** ${srcIp}
                    
                    **目标IP：** ${dstIp}
                    
                    **协议：** ${protocol}
                    
                    **发生时间：** ${eventTimestamp}
                    
                    **处理时间：** ${processedTimestamp}
                    
                    **置信度：** ${confidence}
                    
                    **风险评分：** ${riskScore}
                    
                    **描述：** ${description}
                    """;
            }
            default -> {
                titleTemplate = "【告警通知】${alarmType} - ${alarmName}";
                contentTemplate = "告警类型：${alarmType}，告警名称：${alarmName}，源IP：${srcIp}，目标IP：${dstIp}，时间：${eventTimestamp}";
            }
        }
        
        return NotificationTemplate.builder()
                .templateId("default_" + channelType.getCode())
                .templateName("默认" + channelType.getDisplayName() + "模板")
                .templateType(TemplateType.ALARM_NOTIFICATION)
                .channelType(channelType)
                .titleTemplate(titleTemplate)
                .contentTemplate(contentTemplate)
                .enabled(true)
                .language(TemplateLanguage.SIMPLE)
                .version("1.0")
                .createTime(LocalDateTime.now())
                .creator("system")
                .description("系统默认的" + channelType.getDisplayName() + "告警通知模板")
                .build();
    }
    
    /**
     * 验证模板配置是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        if (templateId == null || templateId.trim().isEmpty()) {
            return false;
        }
        
        if (!Boolean.TRUE.equals(enabled)) {
            return false;
        }
        
        if (templateType == null || channelType == null) {
            return false;
        }
        
        if (contentTemplate == null || contentTemplate.trim().isEmpty()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 渲染模板
     * 
     * @param context 渲染上下文
     * @return 渲染后的模板
     */
    public RenderedTemplate render(Map<String, Object> context) {
        if (!isValid() || context == null) {
            return null;
        }
        
        try {
            String renderedTitle = renderTemplate(titleTemplate, context);
            String renderedContent = renderTemplate(contentTemplate, context);
            
            return RenderedTemplate.builder()
                    .title(renderedTitle)
                    .content(renderedContent)
                    .renderTime(LocalDateTime.now())
                    .templateId(templateId)
                    .context(context)
                    .build();
                    
        } catch (Exception e) {
            // 渲染失败，返回null
            return null;
        }
    }
    
    /**
     * 渲染单个模板字符串
     * 
     * @param template 模板字符串
     * @param context 上下文
     * @return 渲染后的字符串
     */
    private String renderTemplate(String template, Map<String, Object> context) {
        if (template == null || template.trim().isEmpty()) {
            return "";
        }
        
        // 根据模板语言选择渲染方式
        switch (language != null ? language : TemplateLanguage.SIMPLE) {
            case SIMPLE -> {
                return renderSimpleTemplate(template, context);
            }
            case FREEMARKER -> {
                return renderFreemarkerTemplate(template, context);
            }
            case VELOCITY -> {
                return renderVelocityTemplate(template, context);
            }
            case THYMELEAF -> {
                return renderThymeleafTemplate(template, context);
            }
            default -> {
                return renderSimpleTemplate(template, context);
            }
        }
    }
    
    /**
     * 简单变量替换渲染
     */
    private String renderSimpleTemplate(String template, Map<String, Object> context) {
        String result = template;
        
        // 使用正则表达式匹配 ${variableName} 格式的变量
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(template);
        
        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object value = context.get(variableName);
            String replacement = value != null ? formatValue(value) : "";
            result = result.replace(matcher.group(0), replacement);
        }
        
        return result;
    }
    
    /**
     * Freemarker模板渲染（简化实现）
     */
    private String renderFreemarkerTemplate(String template, Map<String, Object> context) {
        // 这里应该集成Freemarker引擎
        // 简化实现，使用简单替换
        return renderSimpleTemplate(template, context);
    }
    
    /**
     * Velocity模板渲染（简化实现）
     */
    private String renderVelocityTemplate(String template, Map<String, Object> context) {
        // 这里应该集成Velocity引擎
        // 简化实现，使用简单替换
        return renderSimpleTemplate(template, context);
    }
    
    /**
     * Thymeleaf模板渲染（简化实现）
     */
    private String renderThymeleafTemplate(String template, Map<String, Object> context) {
        // 这里应该集成Thymeleaf引擎
        // 简化实现，使用简单替换
        return renderSimpleTemplate(template, context);
    }
    
    /**
     * 格式化值
     */
    private String formatValue(Object value) {
        if (value == null) {
            return "";
        }
        
        if (value instanceof LocalDateTime dateTime) {
            return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        
        if (value instanceof Double || value instanceof Float) {
            return String.format("%.2f", value);
        }
        
        return value.toString();
    }
    
    /**
     * 获取模板的显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        if (templateName != null && !templateName.trim().isEmpty()) {
            return templateName;
        }
        return templateId;
    }
    
    /**
     * 获取模板的简要描述
     * 
     * @return 简要描述
     */
    public String getBriefDescription() {
        if (description != null && !description.trim().isEmpty()) {
            return description.length() > 100 ? description.substring(0, 100) + "..." : description;
        }
        
        StringBuilder sb = new StringBuilder();
        if (templateType != null) {
            sb.append(templateType.getDisplayName());
        }
        if (channelType != null) {
            sb.append(" - ").append(channelType.getDisplayName());
        }
        
        return sb.toString();
    }
    
    /**
     * 检查模板是否支持指定的渠道类型
     * 
     * @param channelType 渠道类型
     * @return 是否支持
     */
    public boolean supportsChannelType(NotificationChannel.ChannelType channelType) {
        return this.channelType == null || this.channelType == channelType;
    }
    
    /**
     * 获取扩展属性
     * 
     * @param key 属性键
     * @return 属性值
     */
    public Object getProperty(String key) {
        if (properties == null) {
            return null;
        }
        return properties.get(key);
    }
    
    /**
     * 设置扩展属性
     * 
     * @param key 属性键
     * @param value 属性值
     */
    public void setProperty(String key, Object value) {
        if (properties == null) {
            properties = new java.util.HashMap<>();
        }
        properties.put(key, value);
    }
}
