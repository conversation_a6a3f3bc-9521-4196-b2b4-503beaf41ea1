package com.geeksec.alarmprocessor.notification.function;

import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.notification.sender.SimplifiedNotificationSender;
import com.geeksec.nta.alarm.dto.subscription.NotificationChannelDto;
import com.geeksec.nta.alarm.dto.subscription.NotificationResultDto;
import com.geeksec.nta.alarm.dto.subscription.NotificationSubscriptionDto;
import com.geeksec.nta.alarm.dto.subscription.SubscriptionRuleDto;
import com.geeksec.nta.alarm.entity.NotificationLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

/**
 * 简化的通知处理函数
 * 处理告警事件和订阅配置的广播流，实现告警通知发送
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class SimplifiedNotificationFunction extends BroadcastProcessFunction<Alarm, NotificationSubscriptionDto, NotificationResultDto> {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订阅配置状态描述符
     */
    public static final MapStateDescriptor<String, NotificationSubscriptionDto> SUBSCRIPTION_STATE_DESCRIPTOR =
            new MapStateDescriptor<>("subscription-state", String.class, NotificationSubscriptionDto.class);
    
    private final AlarmProcessorConfig config;
    private transient SimplifiedNotificationSender notificationSender;
    
    public SimplifiedNotificationFunction(AlarmProcessorConfig config) {
        this.config = config;
    }
    
    @Override
    public void open(org.apache.flink.configuration.Configuration parameters) throws Exception {
        super.open(parameters);
        this.notificationSender = new SimplifiedNotificationSender(config);
        log.info("简化通知处理函数已初始化");
    }
    
    @Override
    public void close() throws Exception {
        if (notificationSender != null) {
            notificationSender.close();
        }
        super.close();
    }
    
    @Override
    public void processElement(Alarm alarm, ReadOnlyContext ctx, Collector<NotificationResultDto> out) throws Exception {
        ReadOnlyBroadcastState<String, NotificationSubscriptionDto> subscriptionState = 
                ctx.getBroadcastState(SUBSCRIPTION_STATE_DESCRIPTOR);
        
        log.debug("处理告警通知: {}", alarm.getAlarmId());
        
        List<NotificationSubscriptionDto> matchedSubscriptions = new ArrayList<>();
        
        // 遍历所有订阅，找到匹配的订阅
        for (Map.Entry<String, NotificationSubscriptionDto> entry : subscriptionState.immutableEntries()) {
            NotificationSubscriptionDto subscription = entry.getValue();
            
            if (Boolean.TRUE.equals(subscription.getEnabled()) && matchesSubscription(alarm, subscription)) {
                matchedSubscriptions.add(subscription);
                log.debug("告警 {} 匹配订阅: {}", alarm.getAlarmId(), subscription.getSubscriptionId());
            }
        }
        
        if (matchedSubscriptions.isEmpty()) {
            log.debug("告警 {} 未匹配任何订阅", alarm.getAlarmId());
            return;
        }
        
        // 发送通知
        for (NotificationSubscriptionDto subscription : matchedSubscriptions) {
            sendNotifications(alarm, subscription, out);
        }
    }
    
    @Override
    public void processBroadcastElement(NotificationSubscriptionDto subscription, Context ctx, Collector<NotificationResultDto> out) throws Exception {
        BroadcastState<String, NotificationSubscriptionDto> subscriptionState = 
                ctx.getBroadcastState(SUBSCRIPTION_STATE_DESCRIPTOR);
        
        if (Boolean.TRUE.equals(subscription.getEnabled())) {
            subscriptionState.put(subscription.getSubscriptionId(), subscription);
            log.debug("更新订阅配置: {}", subscription.getSubscriptionId());
        } else {
            subscriptionState.remove(subscription.getSubscriptionId());
            log.debug("移除订阅配置: {}", subscription.getSubscriptionId());
        }
    }
    
    /**
     * 检查告警是否匹配订阅规则
     */
    private boolean matchesSubscription(Alarm alarm, NotificationSubscriptionDto subscription) {
        List<SubscriptionRuleDto> rules = subscription.getRules();
        if (rules == null || rules.isEmpty()) {
            return false;
        }
        
        // 所有规则都必须匹配（AND逻辑）
        for (SubscriptionRuleDto rule : rules) {
            if (!matchesRule(alarm, rule)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查告警是否匹配单个规则
     */
    private boolean matchesRule(Alarm alarm, SubscriptionRuleDto rule) {
        String fieldName = rule.getFieldName();
        String expectedValue = rule.getExpectedValue();
        String actualValue = getFieldValue(alarm, fieldName);
        
        if (actualValue == null) {
            return false;
        }
        
        boolean ignoreCase = Boolean.TRUE.equals(rule.getIgnoreCase());
        if (ignoreCase) {
            actualValue = actualValue.toLowerCase();
            expectedValue = expectedValue.toLowerCase();
        }
        
        switch (rule.getOperator()) {
            case EQUALS:
                return actualValue.equals(expectedValue);
            case NOT_EQUALS:
                return !actualValue.equals(expectedValue);
            case CONTAINS:
                return actualValue.contains(expectedValue);
            case NOT_CONTAINS:
                return !actualValue.contains(expectedValue);
            case REGEX:
                try {
                    return Pattern.matches(expectedValue, actualValue);
                } catch (Exception e) {
                    log.warn("正则表达式匹配失败: {}", expectedValue, e);
                    return false;
                }
            case GREATER_THAN:
                return compareNumeric(actualValue, expectedValue) > 0;
            case LESS_THAN:
                return compareNumeric(actualValue, expectedValue) < 0;
            case GREATER_THAN_OR_EQUAL:
                return compareNumeric(actualValue, expectedValue) >= 0;
            case LESS_THAN_OR_EQUAL:
                return compareNumeric(actualValue, expectedValue) <= 0;
            case IN:
                String[] values = expectedValue.split(",");
                for (String value : values) {
                    if (actualValue.equals(value.trim())) {
                        return true;
                    }
                }
                return false;
            case NOT_IN:
                String[] notInValues = expectedValue.split(",");
                for (String value : notInValues) {
                    if (actualValue.equals(value.trim())) {
                        return false;
                    }
                }
                return true;
            default:
                log.warn("不支持的操作符: {}", rule.getOperator());
                return false;
        }
    }
    
    /**
     * 获取告警对象的字段值
     */
    private String getFieldValue(Alarm alarm, String fieldName) {
        switch (fieldName.toLowerCase()) {
            case "alarmtype":
            case "alarm_type":
                return alarm.getAlarmType();
            case "alarmlevel":
            case "alarm_level":
                return alarm.getAlarmLevel() != null ? alarm.getAlarmLevel().toString() : null;
            case "srcip":
            case "src_ip":
                return alarm.getSrcIp();
            case "dstip":
            case "dst_ip":
                return alarm.getDstIp();
            case "threattype":
            case "threat_type":
                return alarm.getThreatType();
            case "attackfamily":
            case "attack_family":
                return alarm.getAttackFamily();
            default:
                log.debug("未知字段名: {}", fieldName);
                return null;
        }
    }
    
    /**
     * 数值比较
     */
    private int compareNumeric(String actual, String expected) {
        try {
            double actualNum = Double.parseDouble(actual);
            double expectedNum = Double.parseDouble(expected);
            return Double.compare(actualNum, expectedNum);
        } catch (NumberFormatException e) {
            // 如果不是数字，则按字符串比较
            return actual.compareTo(expected);
        }
    }
    
    /**
     * 发送通知
     */
    private void sendNotifications(Alarm alarm, NotificationSubscriptionDto subscription, Collector<NotificationResultDto> out) {
        List<NotificationChannelDto> channels = subscription.getChannels();
        if (channels == null || channels.isEmpty()) {
            return;
        }
        
        for (NotificationChannelDto channel : channels) {
            if (!Boolean.TRUE.equals(channel.getEnabled())) {
                continue;
            }
            
            // 准备通知内容
            String subject = prepareNotificationSubject(alarm, subscription);
            String content = prepareNotificationContent(alarm, subscription);
            
            // 异步发送通知
            CompletableFuture<SimplifiedNotificationSender.NotificationResult> future = 
                    notificationSender.sendNotification(channel, subject, content);
            
            future.whenComplete((result, throwable) -> {
                NotificationResultDto resultDto;
                if (throwable != null) {
                    resultDto = NotificationResultDto.failure(
                            subscription.getSubscriptionId(),
                            alarm.getAlarmId(),
                            convertChannelType(channel.getChannelType()),
                            channel.getAddress(),
                            throwable.getMessage(),
                            0
                    );
                } else if (result.isSuccess()) {
                    resultDto = NotificationResultDto.success(
                            subscription.getSubscriptionId(),
                            alarm.getAlarmId(),
                            convertChannelType(channel.getChannelType()),
                            channel.getAddress(),
                            subject,
                            content
                    );
                } else {
                    resultDto = NotificationResultDto.failure(
                            subscription.getSubscriptionId(),
                            alarm.getAlarmId(),
                            convertChannelType(channel.getChannelType()),
                            channel.getAddress(),
                            result.getMessage(),
                            0
                    );
                }
                
                out.collect(resultDto);
            });
        }
    }
    
    /**
     * 准备通知主题
     */
    private String prepareNotificationSubject(Alarm alarm, NotificationSubscriptionDto subscription) {
        return String.format("【NTA安全告警】%s - %s", 
                alarm.getAlarmType(), 
                alarm.getAlarmLevel());
    }
    
    /**
     * 准备通知内容
     */
    private String prepareNotificationContent(Alarm alarm, NotificationSubscriptionDto subscription) {
        StringBuilder content = new StringBuilder();
        content.append("安全告警通知\n");
        content.append("================\n");
        content.append("告警类型: ").append(alarm.getAlarmType()).append("\n");
        content.append("告警级别: ").append(alarm.getAlarmLevel()).append("\n");
        content.append("告警时间: ").append(alarm.getAlarmTime()).append("\n");
        content.append("源IP: ").append(alarm.getSrcIp()).append("\n");
        content.append("目标IP: ").append(alarm.getDstIp()).append("\n");
        if (alarm.getDescription() != null) {
            content.append("告警描述: ").append(alarm.getDescription()).append("\n");
        }
        content.append("================\n");
        content.append("请及时处理该安全告警。");
        
        return content.toString();
    }
    
    /**
     * 转换渠道类型
     */
    private NotificationLog.ChannelType convertChannelType(NotificationChannelDto.ChannelType channelType) {
        switch (channelType) {
            case EMAIL:
                return NotificationLog.ChannelType.EMAIL;
            case KAFKA:
                return NotificationLog.ChannelType.KAFKA;
            default:
                throw new IllegalArgumentException("不支持的渠道类型: " + channelType);
        }
    }
}
