package com.geeksec.alarmprocessor.notification.sender;

import com.geeksec.alarmprocessor.notification.model.NotificationChannel;
import com.geeksec.alarmprocessor.notification.model.NotificationTemplate;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 通知发送器
 * 负责根据不同的渠道类型发送通知
 * 
 * <AUTHOR>
 */
@Slf4j
public class NotificationSender implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 渠道发送器映射
     */
    private final Map<NotificationChannel.ChannelType, ChannelSender> channelSenders;
    
    /**
     * 构造函数
     */
    public NotificationSender() {
        this.channelSenders = new ConcurrentHashMap<>();
        initializeDefaultSenders();
    }
    
    /**
     * 初始化默认发送器
     */
    private void initializeDefaultSenders() {
        // 注册默认的渠道发送器
        channelSenders.put(NotificationChannel.ChannelType.EMAIL, new EmailSender());
        channelSenders.put(NotificationChannel.ChannelType.SMS, new SmsSender());
        channelSenders.put(NotificationChannel.ChannelType.DINGTALK, new DingTalkSender());
        channelSenders.put(NotificationChannel.ChannelType.WECHAT_WORK, new WeChatWorkSender());
        channelSenders.put(NotificationChannel.ChannelType.FEISHU, new FeishuSender());
        channelSenders.put(NotificationChannel.ChannelType.WEBHOOK, new WebhookSender());
        
        log.info("初始化通知发送器完成，支持 {} 种渠道类型", channelSenders.size());
    }
    
    /**
     * 发送通知
     * 
     * @param channel 通知渠道
     * @param template 渲染后的模板
     * @return 是否发送成功
     */
    public boolean sendNotification(NotificationChannel channel, NotificationTemplate.RenderedTemplate template) {
        if (channel == null || !channel.isAvailable()) {
            log.warn("通知渠道不可用: {}", channel != null ? channel.getChannelId() : "null");
            return false;
        }
        
        if (template == null) {
            log.warn("通知模板为空");
            return false;
        }
        
        ChannelSender sender = channelSenders.get(channel.getChannelType());
        if (sender == null) {
            log.warn("不支持的通知渠道类型: {}", channel.getChannelType());
            return false;
        }
        
        try {
            log.debug("开始发送通知: 渠道={}, 类型={}, 地址={}", 
                    channel.getChannelId(), channel.getChannelType(), channel.getAddress());
            
            boolean result = sender.send(channel, template);
            
            if (result) {
                log.debug("通知发送成功: 渠道={}, 地址={}", channel.getChannelId(), channel.getAddress());
            } else {
                log.warn("通知发送失败: 渠道={}, 地址={}", channel.getChannelId(), channel.getAddress());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("发送通知时发生异常: 渠道={}, 地址={}, 错误={}", 
                    channel.getChannelId(), channel.getAddress(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 添加自定义渠道发送器
     * 
     * @param channelType 渠道类型
     * @param sender 发送器
     */
    public void addChannelSender(NotificationChannel.ChannelType channelType, ChannelSender sender) {
        channelSenders.put(channelType, sender);
        log.info("添加自定义渠道发送器: {}", channelType);
    }
    
    /**
     * 渠道发送器接口
     */
    public interface ChannelSender extends Serializable {
        /**
         * 发送通知
         * 
         * @param channel 通知渠道
         * @param template 渲染后的模板
         * @return 是否发送成功
         */
        boolean send(NotificationChannel channel, NotificationTemplate.RenderedTemplate template);
    }
    
    /**
     * 邮件发送器
     */
    public static class EmailSender implements ChannelSender {
        private static final long serialVersionUID = 1L;
        
        @Override
        public boolean send(NotificationChannel channel, NotificationTemplate.RenderedTemplate template) {
            // 这里应该集成实际的邮件发送服务
            log.info("发送邮件通知到: {}, 标题: {}", channel.getAddress(), template.getTitle());
            
            // 模拟发送过程
            try {
                Thread.sleep(100); // 模拟网络延迟
                
                // 模拟发送成功率（90%）
                return Math.random() > 0.1;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
    }
    
    /**
     * 短信发送器
     */
    public static class SmsSender implements ChannelSender {
        private static final long serialVersionUID = 1L;
        
        @Override
        public boolean send(NotificationChannel channel, NotificationTemplate.RenderedTemplate template) {
            // 这里应该集成实际的短信发送服务
            log.info("发送短信通知到: {}, 内容: {}", channel.getAddress(), template.getContent());
            
            // 模拟发送过程
            try {
                Thread.sleep(50); // 模拟网络延迟
                
                // 模拟发送成功率（95%）
                return Math.random() > 0.05;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
    }
    
    /**
     * 钉钉发送器
     */
    public static class DingTalkSender implements ChannelSender {
        private static final long serialVersionUID = 1L;
        
        @Override
        public boolean send(NotificationChannel channel, NotificationTemplate.RenderedTemplate template) {
            // 这里应该集成钉钉机器人API
            log.info("发送钉钉通知到: {}, 标题: {}", channel.getAddress(), template.getTitle());
            
            try {
                // 构建钉钉消息格式
                Map<String, Object> message = new ConcurrentHashMap<>();
                message.put("msgtype", "markdown");
                
                Map<String, Object> markdown = new ConcurrentHashMap<>();
                markdown.put("title", template.getTitle());
                markdown.put("text", template.getContent());
                message.put("markdown", markdown);
                
                // 模拟HTTP请求发送
                Thread.sleep(200);
                
                // 模拟发送成功率（85%）
                return Math.random() > 0.15;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
    }
    
    /**
     * 企业微信发送器
     */
    public static class WeChatWorkSender implements ChannelSender {
        private static final long serialVersionUID = 1L;
        
        @Override
        public boolean send(NotificationChannel channel, NotificationTemplate.RenderedTemplate template) {
            // 这里应该集成企业微信API
            log.info("发送企业微信通知到: {}, 标题: {}", channel.getAddress(), template.getTitle());
            
            try {
                Thread.sleep(150);
                
                // 模拟发送成功率（88%）
                return Math.random() > 0.12;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
    }
    
    /**
     * Webhook发送器
     */
    public static class WebhookSender implements ChannelSender {
        private static final long serialVersionUID = 1L;
        
        @Override
        public boolean send(NotificationChannel channel, NotificationTemplate.RenderedTemplate template) {
            // 这里应该发送HTTP请求到指定的Webhook地址
            log.info("发送Webhook通知到: {}, 标题: {}", channel.getAddress(), template.getTitle());
            
            try {
                Thread.sleep(100);
                
                // 模拟发送成功率（92%）
                return Math.random() > 0.08;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
    }
    
    /**
     * 飞书发送器
     */
    public static class FeishuSender implements ChannelSender {
        private static final long serialVersionUID = 1L;
        
        @Override
        public boolean send(NotificationChannel channel, NotificationTemplate.RenderedTemplate template) {
            // 这里应该集成飞书机器人API
            log.info("发送飞书通知到: {}, 标题: {}", channel.getAddress(), template.getTitle());
            
            try {
                Thread.sleep(180);
                
                // 模拟发送成功率（87%）
                return Math.random() > 0.13;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
    }
    
    /**
     * 获取支持的渠道类型
     * 
     * @return 支持的渠道类型列表
     */
    public java.util.Set<NotificationChannel.ChannelType> getSupportedChannelTypes() {
        return channelSenders.keySet();
    }
    
    /**
     * 检查是否支持指定的渠道类型
     * 
     * @param channelType 渠道类型
     * @return 是否支持
     */
    public boolean supportsChannelType(NotificationChannel.ChannelType channelType) {
        return channelSenders.containsKey(channelType);
    }
    
    /**
     * 获取渠道发送器
     * 
     * @param channelType 渠道类型
     * @return 发送器实例
     */
    public ChannelSender getChannelSender(NotificationChannel.ChannelType channelType) {
        return channelSenders.get(channelType);
    }
    
    /**
     * 移除渠道发送器
     * 
     * @param channelType 渠道类型
     */
    public void removeChannelSender(NotificationChannel.ChannelType channelType) {
        channelSenders.remove(channelType);
        log.info("移除渠道发送器: {}", channelType);
    }
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    public NotificationStatistics getStatistics() {
        return new NotificationStatistics(
                channelSenders.size(),
                0L, // 这里应该记录实际的发送统计
                0L,
                0L
        );
    }
    
    /**
     * 通知统计信息
     */
    public record NotificationStatistics(
            int supportedChannelCount,
            long totalSent,
            long totalSuccess,
            long totalFailure
    ) implements Serializable {
        
        public double getSuccessRate() {
            if (totalSent == 0) {
                return 0.0;
            }
            return (double) totalSuccess / totalSent;
        }
    }
}
