package com.geeksec.alarmprocessor.notification.sync;

import com.geeksec.alarmprocessor.notification.client.AlarmServiceClient;
import com.geeksec.alarmprocessor.notification.manager.NotificationManager;
import com.geeksec.alarmprocessor.notification.model.NotificationSubscription;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订阅配置同步服务
 * 负责从 services/alarm 模块同步订阅配置到 alarm-processor
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class SubscriptionConfigSyncService implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmServiceClient alarmServiceClient;
    private final NotificationManager notificationManager;
    private final ScheduledExecutorService scheduler;
    private final int syncIntervalSeconds;
    
    private volatile LocalDateTime lastSyncTime;
    private volatile boolean running = false;
    
    public SubscriptionConfigSyncService(AlarmServiceClient alarmServiceClient,
                                       NotificationManager notificationManager,
                                       int syncIntervalSeconds) {
        this.alarmServiceClient = alarmServiceClient;
        this.notificationManager = notificationManager;
        this.syncIntervalSeconds = syncIntervalSeconds;
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "subscription-sync-thread");
            t.setDaemon(true);
            return t;
        });
        this.lastSyncTime = LocalDateTime.now().minusHours(1); // 初始化为1小时前
        
        log.info("订阅配置同步服务初始化完成，同步间隔: {} 秒", syncIntervalSeconds);
    }
    
    /**
     * 启动同步服务
     */
    public void start() {
        if (running) {
            log.warn("订阅配置同步服务已经在运行中");
            return;
        }
        
        running = true;
        
        // 立即执行一次全量同步
        CompletableFuture.runAsync(this::performFullSync, scheduler);
        
        // 定期执行增量同步
        scheduler.scheduleWithFixedDelay(
                this::performIncrementalSync,
                syncIntervalSeconds,
                syncIntervalSeconds,
                TimeUnit.SECONDS
        );
        
        log.info("订阅配置同步服务已启动");
    }
    
    /**
     * 停止同步服务
     */
    public void stop() {
        if (!running) {
            return;
        }
        
        running = false;
        scheduler.shutdown();
        
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        log.info("订阅配置同步服务已停止");
    }
    
    /**
     * 执行全量同步
     */
    private void performFullSync() {
        try {
            log.debug("开始执行全量订阅配置同步");
            
            List<NotificationSubscription> subscriptions = 
                    alarmServiceClient.getAllActiveSubscriptions().get(30, TimeUnit.SECONDS);
            
            if (subscriptions != null) {
                Map<String, NotificationSubscription> subscriptionMap = subscriptions.stream()
                        .collect(Collectors.toMap(
                                NotificationSubscription::getSubscriptionId,
                                subscription -> subscription
                        ));
                
                notificationManager.updateSubscriptions(subscriptionMap);
                lastSyncTime = LocalDateTime.now();
                
                log.info("全量订阅配置同步完成，共同步 {} 个订阅", subscriptions.size());
            } else {
                log.warn("全量同步获取到空的订阅配置");
            }
            
        } catch (Exception e) {
            log.error("全量订阅配置同步失败", e);
        }
    }
    
    /**
     * 执行增量同步
     */
    private void performIncrementalSync() {
        if (!running) {
            return;
        }
        
        try {
            log.debug("开始执行增量订阅配置同步，上次同步时间: {}", lastSyncTime);
            
            // 检查服务健康状态
            boolean healthy = alarmServiceClient.checkHealth().get(5, TimeUnit.SECONDS);
            if (!healthy) {
                log.warn("告警服务不健康，跳过本次增量同步");
                return;
            }
            
            // 获取更新的订阅配置（这里需要扩展 AlarmServiceClient 支持增量获取）
            // 暂时执行全量同步
            performFullSync();
            
        } catch (Exception e) {
            log.error("增量订阅配置同步失败", e);
        }
    }
    
    /**
     * 手动触发同步
     */
    public CompletableFuture<Void> triggerSync() {
        return CompletableFuture.runAsync(this::performFullSync, scheduler);
    }
    
    /**
     * 获取同步状态
     */
    public SyncStatus getSyncStatus() {
        return new SyncStatus(running, lastSyncTime, 
                notificationManager.getSubscriptionCount());
    }
    
    /**
     * 同步状态
     */
    public static class SyncStatus implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private final boolean running;
        private final LocalDateTime lastSyncTime;
        private final int subscriptionCount;
        
        public SyncStatus(boolean running, LocalDateTime lastSyncTime, int subscriptionCount) {
            this.running = running;
            this.lastSyncTime = lastSyncTime;
            this.subscriptionCount = subscriptionCount;
        }
        
        public boolean isRunning() {
            return running;
        }
        
        public LocalDateTime getLastSyncTime() {
            return lastSyncTime;
        }
        
        public int getSubscriptionCount() {
            return subscriptionCount;
        }
        
        @Override
        public String toString() {
            return String.format("SyncStatus{running=%s, lastSyncTime=%s, subscriptionCount=%d}",
                    running, lastSyncTime, subscriptionCount);
        }
    }
    
    /**
     * 创建默认同步服务
     */
    public static SubscriptionConfigSyncService createDefault(String alarmServiceBaseUrl,
                                                             NotificationManager notificationManager) {
        AlarmServiceClient client = new AlarmServiceClient(alarmServiceBaseUrl);
        return new SubscriptionConfigSyncService(client, notificationManager, 30); // 30秒同步一次
    }
    
    /**
     * 创建快速同步服务
     */
    public static SubscriptionConfigSyncService createFastSync(String alarmServiceBaseUrl,
                                                              NotificationManager notificationManager,
                                                              int syncIntervalSeconds) {
        AlarmServiceClient client = new AlarmServiceClient(alarmServiceBaseUrl);
        return new SubscriptionConfigSyncService(client, notificationManager, syncIntervalSeconds);
    }
}
